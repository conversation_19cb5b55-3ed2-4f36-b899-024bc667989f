"""
Flask 應用程式配置
支援模組化前端架構和多環境部署
"""

import os
from pathlib import Path


class Config:
    """
    基礎配置類別
    """
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)
    
    # Flask 應用程式配置
    FLASK_APP = os.environ.get('FLASK_APP') or 'frontend.app:create_app'
    FLASK_ENV = os.environ.get('FLASK_ENV') or 'development'
    FLASK_DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() in ['true', '1', 'yes']
    
    # 伺服器配置
    HOST = os.environ.get('FLASK_HOST') or '0.0.0.0'
    PORT = int(os.environ.get('FLASK_PORT') or 5000)
    
    # 檔案上傳配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'temp/uploads'
    
    # 模板和靜態檔案配置
    TEMPLATES_AUTO_RELOAD = True
    SEND_FILE_MAX_AGE_DEFAULT = 0
    
    # 模組化目錄結構配置
    FRONTEND_ROOT = Path(__file__).parent  # frontend/ 目錄
    PROJECT_ROOT = FRONTEND_ROOT.parent    # 專案根目錄
    
    # 模組化靜態資源配置
    STATIC_FOLDER_MAPPING = {
        'shared': 'frontend/shared/static',
        'email': 'frontend/email/static',
        'analytics': 'frontend/analytics/static',
        'file_management': 'frontend/file_management/static',
        'eqc': 'frontend/eqc/static',
        'tasks': 'frontend/tasks/static',
        'monitoring': 'frontend/monitoring/static'
    }
    
    # 模組化模板配置
    TEMPLATE_FOLDER_MAPPING = {
        'shared': 'frontend/shared/templates',
        'email': 'frontend/email/templates',
        'analytics': 'frontend/analytics/templates',
        'file_management': 'frontend/file_management/templates',
        'eqc': 'frontend/eqc/templates',
        'tasks': 'frontend/tasks/templates',
        'monitoring': 'frontend/monitoring/templates'
    }
    
    # 資料庫配置
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///data/email_inbox.db'
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 後端服務配置
    FLASK_EMAIL_SERVICE_HOST = os.environ.get('FLASK_EMAIL_SERVICE_HOST') or '127.0.0.1'
    FLASK_EMAIL_SERVICE_PORT = int(os.environ.get('FLASK_EMAIL_SERVICE_PORT') or 5000)
    
    FASTAPI_EQC_SERVICE_HOST = os.environ.get('FASTAPI_EQC_SERVICE_HOST') or '127.0.0.1'
    FASTAPI_EQC_SERVICE_PORT = int(os.environ.get('FASTAPI_EQC_SERVICE_PORT') or 8010)
    
    # Redis 配置
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB = int(os.environ.get('REDIS_DB') or 0)
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')
    
    # WebSocket 配置
    WEBSOCKET_HOST = os.environ.get('WEBSOCKET_HOST') or '127.0.0.1'
    WEBSOCKET_PORT = int(os.environ.get('WEBSOCKET_PORT') or 8765)
    
    # 通知服務配置
    LINE_CHANNEL_ACCESS_TOKEN = os.environ.get('LINE_CHANNEL_ACCESS_TOKEN')
    LINE_USER_ID = os.environ.get('LINE_USER_ID')
    
    # 日誌配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_DIR = os.environ.get('LOG_DIR') or 'logs'
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour
    
    # API 配置
    API_TIMEOUT = int(os.environ.get('API_TIMEOUT') or 30)
    API_RETRY_COUNT = int(os.environ.get('API_RETRY_COUNT') or 3)
    
    # 效能配置
    CACHE_TYPE = os.environ.get('CACHE_TYPE') or 'simple'
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT') or 300)
    
    @staticmethod
    def init_app(app):
        """初始化應用程式配置"""
        # 確保必要目錄存在
        for directory in ['logs', 'temp', 'data', 'temp/uploads']:
            Path(directory).mkdir(parents=True, exist_ok=True)


class DevelopmentConfig(Config):
    """
    開發環境配置
    """
    DEBUG = True
    TESTING = False


class TestingConfig(Config):
    """
    測試環境配置
    """
    DEBUG = False
    TESTING = True
    DATABASE_URL = 'sqlite:///:memory:'


class ProductionConfig(Config):
    """
    生產環境配置
    """
    DEBUG = False
    TESTING = False
    FLASK_DEBUG = False
    
    # 生產環境使用更安全的設定
    TEMPLATES_AUTO_RELOAD = False
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 year
    
    # 安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 效能配置
    CACHE_TYPE = os.environ.get('CACHE_TYPE') or 'redis'
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT') or 3600)
    
    # 日誌配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'WARNING'
    
    @staticmethod
    def init_app(app):
        """生產環境特定初始化"""
        Config.init_app(app)
        
        # 設定日誌
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            # 檔案日誌處理器
            log_dir = Path(app.config.get('LOG_DIR', 'logs'))
            log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = RotatingFileHandler(
                log_dir / 'frontend.log',
                maxBytes=10240000,  # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Frontend application startup')


# 配置映射
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}