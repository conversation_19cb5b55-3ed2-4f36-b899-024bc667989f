markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v5.1
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v5.1
  architectureSharded: true
  architectureShardedLocation: docs/architecture
customTechnicalDocuments: 
  - .kiro/specs/vue-frontend-migration/design.md
  - .kiro/specs/vue-frontend-migration/requirements.md
  - .kiro/specs/vue-frontend-migration/tasks.md
  - .kiro/steering/frontend-refactor.md
  - .kiro/steering/tech.md
devLoadAlwaysFiles:
  - docs/architecture/coding-standards.md
  - docs/02_ARCHITECTURE/tech-stack.md
  - docs/architecture/source-tree.md
  - .kiro/specs/vue-frontend-migration/design.md
  - .kiro/steering/frontend-refactor.md
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad
