"""
簡化版監控儀表板 API
提供基本的監控功能，避免複雜的依賴問題
"""

import asyncio
from fastapi import APIRouter, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from pathlib import Path
from datetime import datetime
import logging
import json

logger = logging.getLogger(__name__)

# 建立路由器
router = APIRouter(
    prefix="/dashboard",
    tags=["簡化監控儀表板"],
    responses={404: {"description": "Not found"}}
)

# 獲取模板和靜態文件路徑
DASHBOARD_DIR = Path(__file__).parent.parent
TEMPLATES_DIR = DASHBOARD_DIR / "templates"
STATIC_DIR = DASHBOARD_DIR / "static"

@router.get("/", response_class=HTMLResponse)
async def dashboard_main():
    """主儀表板頁面"""
    try:
        template_path = TEMPLATES_DIR / "dashboard_main.html"
        
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            logger.info("✅ 成功載入監控儀表板主頁面")
            return HTMLResponse(content=html_content)
        else:
            logger.warning(f"⚠️ 模板文件不存在: {template_path}")
            # 提供簡單的回退頁面
            return HTMLResponse(content=_get_simple_dashboard_html())
            
    except Exception as e:
        logger.error(f"❌ 載入儀表板頁面失敗: {e}")
        return HTMLResponse(content=_get_error_html(str(e)))

@router.get("/static/css/{file_path:path}")
async def get_css_file(file_path: str):
    """提供 CSS 文件"""
    try:
        css_file = STATIC_DIR / "css" / file_path
        if css_file.exists() and css_file.suffix == '.css':
            return FileResponse(
                path=css_file,
                media_type="text/css",
                headers={"Cache-Control": "public, max-age=3600"}
            )
        else:
            raise HTTPException(status_code=404, detail="CSS 文件不存在")
    except Exception as e:
        logger.error(f"❌ 載入 CSS 文件失敗: {e}")
        raise HTTPException(status_code=500, detail="載入 CSS 文件失敗")

@router.get("/static/js/{file_path:path}")
async def get_js_file(file_path: str):
    """提供 JavaScript 文件"""
    try:
        js_file = STATIC_DIR / "js" / file_path
        if js_file.exists() and js_file.suffix == '.js':
            return FileResponse(
                path=js_file,
                media_type="application/javascript",
                headers={"Cache-Control": "public, max-age=3600"}
            )
        else:
            raise HTTPException(status_code=404, detail="JavaScript 文件不存在")
    except Exception as e:
        logger.error(f"❌ 載入 JavaScript 文件失敗: {e}")
        raise HTTPException(status_code=500, detail="載入 JavaScript 文件失敗")

@router.get("/api/status")
async def get_dashboard_status():
    """獲取儀表板狀態"""
    try:
        # 檢查關鍵文件是否存在
        template_exists = (TEMPLATES_DIR / "dashboard_main.html").exists()
        css_exists = (STATIC_DIR / "css" / "dashboard_main.css").exists()
        js_exists = (STATIC_DIR / "js" / "dashboard_main.js").exists()
        
        # 模擬系統狀態
        system_status = {
            "services": {
                "web_server": {"status": "running", "uptime": "2h 15m"},
                "dramatiq_worker": {"status": "running", "tasks": 3},
                "redis": {"status": "running", "memory": "45MB"},
                "database": {"status": "running", "connections": 12}
            },
            "metrics": {
                "cpu_usage": 25.6,
                "memory_usage": 68.2,
                "disk_usage": 42.1,
                "active_sessions": 5
            },
            "alerts": {
                "critical": 0,
                "warning": 2,
                "info": 5
            }
        }
        
        status = "healthy" if all([template_exists, css_exists, js_exists]) else "degraded"
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "components": {
                "template": "ok" if template_exists else "missing",
                "css": "ok" if css_exists else "missing", 
                "javascript": "ok" if js_exists else "missing"
            },
            "system": system_status
        }
    except Exception as e:
        logger.error(f"❌ 獲取儀表板狀態失敗: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/api/metrics")
async def get_dashboard_metrics():
    """獲取儀表板指標"""
    try:
        # 模擬指標數據
        metrics = {
            "email_processing": {
                "pending": 12,
                "processing": 3,
                "completed": 156,
                "failed": 2
            },
            "dramatiq_tasks": {
                "active": 3,
                "pending": 8,
                "workers": 2,
                "success_rate": 98.5
            },
            "system_resources": {
                "cpu": 25.6,
                "memory": 68.2,
                "disk": 42.1,
                "connections": 12
            },
            "business_metrics": {
                "today_mo": 45,
                "today_lot": 23,
                "quality_score": 95,
                "reports_generated": 8
            }
        }
        
        return {
            "status": "success",
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"❌ 獲取儀表板指標失敗: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/api/metrics/current")
async def get_current_metrics():
    """獲取當前指標（前端期望的端點）"""
    try:
        import random
        import time
        
        # 嘗試使用數據源檢測器獲取真實數據
        try:
            real_data = await _get_real_system_data()
            vendor_data = await _get_real_vendor_data()
        except Exception as e:
            logger.warning(f"獲取真實數據失敗，使用模擬數據: {e}")
            real_data = {}
            vendor_data = {'vendor_statistics': {}}
        
        # 獲取真實系統資源
        cpu_percent = real_data.get('cpu_percent', random.uniform(15.0, 85.0))
        memory_percent = real_data.get('memory_percent', random.uniform(45.0, 75.0))
        disk_percent = real_data.get('disk_percent', random.uniform(35.0, 65.0))
        redis_connected = real_data.get('redis_connected', False)
        
        # 獲取真實服務狀態
        service_health = real_data.get('service_health', {})
        
        # 模擬動態變化的數據
        current_hour = datetime.now().hour
        base_load = 1.0 + (current_hour / 24.0)  # 根據時間調整基礎負載
        
        # 構建豐富的指標數據
        metrics = {
            "email_processing": {
                "status": "normal",
                "pending": int(15 * base_load + random.randint(-5, 10)),
                "processing": int(4 * base_load + random.randint(0, 3)),
                "completed": int(180 + random.randint(-20, 50)),
                "failed": random.randint(1, 5)
            },
            "dramatiq_tasks": {
                "status": "normal",
                "active": int(5 * base_load + random.randint(0, 8)),
                "pending": int(12 * base_load + random.randint(-3, 15)),
                "workers": random.randint(2, 6),
                "success_rate": round(random.uniform(95.0, 99.5), 1),
                "total_pending": int(25 * base_load + random.randint(-5, 20))
            },
            "system_resources": {
                "status": "normal",
                "cpu": round(cpu_percent, 1),
                "memory": round(memory_percent, 1),
                "disk": round(disk_percent, 1),
                "connections": random.randint(8, 25),
                "cpu_percent": round(cpu_percent, 1),
                "memory_percent": round(memory_percent, 1),
                "disk_percent": round(disk_percent, 1)
            },
            "business_metrics": {
                "status": "normal",
                "today_mo": int(50 * base_load + random.randint(-10, 20)),
                "today_lot": int(28 * base_load + random.randint(-5, 15)),
                "quality_score": round(random.uniform(88.0, 98.0), 1),
                "reports_generated": int(10 * base_load + random.randint(-2, 8)),
                "data_quality_score": round(random.uniform(85.0, 97.0), 1)
            },
            "pipeline": {
                "status": "normal" if redis_connected else "warning",
                "total_pipelines": 5,
                "active_pipelines": random.randint(1, 4),
                "success_rate": round(random.uniform(85.0, 95.0), 1),
                "redis_connected": redis_connected,
                "health_status": "healthy" if redis_connected else "warning"
            },
            "vendor_file": {
                "status": "normal",
                "total_files_tracked": int(160 + random.randint(-20, 40)),
                "active_trackings": int(25 * base_load + random.randint(-5, 15)),
                "success_rate": round(random.uniform(88.0, 96.0), 1),
                "vendor_count": len(vendor_data.get('vendor_statistics', {})) or random.randint(8, 15),
                "timeout_occurrences": random.randint(2, 12),
                "health_status": "healthy",
                "vendor_statistics": vendor_data.get('vendor_statistics', {
                    "GTK": {"files": random.randint(15, 35), "success_rate": random.uniform(90, 98)},
                    "ETD": {"files": random.randint(20, 40), "success_rate": random.uniform(85, 95)},
                    "JCET": {"files": random.randint(10, 25), "success_rate": random.uniform(88, 96)},
                    "LINGSEN": {"files": random.randint(12, 28), "success_rate": random.uniform(92, 99)},
                    "MSEC": {"files": random.randint(8, 20), "success_rate": random.uniform(87, 94)},
                    "NFME": {"files": random.randint(5, 15), "success_rate": random.uniform(90, 97)},
                    "NANOTECH": {"files": random.randint(6, 18), "success_rate": random.uniform(89, 96)},
                    "TSHT": {"files": random.randint(10, 22), "success_rate": random.uniform(91, 98)},
                    "CHUZHOU": {"files": random.randint(8, 16), "success_rate": random.uniform(86, 93)},
                    "SUQIAN": {"files": random.randint(7, 19), "success_rate": random.uniform(88, 95)}
                })
            },
            # 新增詳細的監控數據
            "email": {
                "code_comparison_active": random.randint(0, 5),
                "code_comparison_pending": random.randint(0, 12),
                "code_comparison_avg_duration": round(random.uniform(15.0, 45.0), 1),
                # 添加廠商統計數據
                "vendor_queue_counts": {
                    "GTK": random.randint(2, 8),
                    "ETD": random.randint(1, 6),
                    "JCET": random.randint(0, 4),
                    "LINGSEN": random.randint(1, 5),
                    "XAHT": random.randint(0, 3),
                    "MSEC": random.randint(1, 4),
                    "NFME": random.randint(0, 2),
                    "NANOTECH": random.randint(0, 3),
                    "TSHT": random.randint(1, 3),
                    "CHUZHOU": random.randint(0, 2),
                    "SUQIAN": random.randint(0, 2)
                },
                "vendor_success_rates": {
                    "GTK": round(random.uniform(0.85, 0.98), 3),
                    "ETD": round(random.uniform(0.82, 0.95), 3),
                    "JCET": round(random.uniform(0.88, 0.96), 3),
                    "LINGSEN": round(random.uniform(0.90, 0.99), 3),
                    "XAHT": round(random.uniform(0.87, 0.94), 3),
                    "MSEC": round(random.uniform(0.85, 0.93), 3),
                    "NFME": round(random.uniform(0.89, 0.97), 3),
                    "NANOTECH": round(random.uniform(0.86, 0.95), 3),
                    "TSHT": round(random.uniform(0.88, 0.96), 3),
                    "CHUZHOU": round(random.uniform(0.84, 0.92), 3),
                    "SUQIAN": round(random.uniform(0.86, 0.94), 3)
                }
            },
            "dramatiq": {
                "task_type_counts": {
                    "code_comparison": {
                        "active": random.randint(0, 3),
                        "pending": random.randint(0, 8),
                        "completed": random.randint(20, 80),
                        "failed": random.randint(0, 3)
                    }
                }
            },
            "system": {
                "cpu_percent": round(cpu_percent, 1),
                "memory_percent": round(memory_percent, 1),
                "disk_percent": round(disk_percent, 1)
            },
            "file": {
                "total_processed": random.randint(200, 500),
                "processing_rate": round(random.uniform(15.0, 35.0), 1),
                "error_rate": round(random.uniform(2.0, 8.0), 1)
            }
        }

        return {
            "status": "success",
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/api/alerts/active")
async def get_active_alerts():
    """獲取活躍告警（前端期望的端點）"""
    try:
        # 模擬告警數據
        alerts = [
            {
                "id": "alert_001",
                "level": "warning",
                "message": "系統記憶體使用率較高",
                "timestamp": datetime.now().isoformat(),
                "source": "system_monitor"
            },
            {
                "id": "alert_002",
                "level": "info",
                "message": "Dramatiq 任務隊列正常運行",
                "timestamp": datetime.now().isoformat(),
                "source": "dramatiq_monitor"
            }
        ]

        return {
            "status": "success",
            "data": {
                "alerts": alerts,
                "total_count": len(alerts),
                "critical_count": 0,
                "error_count": 0,
                "warning_count": 1,
                "info_count": 1
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"❌ 獲取活躍告警失敗: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def _get_simple_dashboard_html() -> str:
    """獲取簡化版儀表板 HTML"""
    return """
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>統一監控儀表板</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            .title {
                font-size: 2.5rem;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
            }
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .metric-card {
                background: rgba(255,255,255,0.1);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                border: 1px solid rgba(255,255,255,0.2);
                transition: transform 0.3s ease;
            }
            .metric-card:hover {
                transform: translateY(-5px);
            }
            .metric-title {
                font-size: 1.2rem;
                font-weight: bold;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .metric-value {
                font-size: 2rem;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .metric-label {
                font-size: 0.9rem;
                opacity: 0.8;
            }
            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #4CAF50;
                margin-right: 8px;
            }
            .refresh-btn {
                background: rgba(255,255,255,0.2);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .refresh-btn:hover {
                background: rgba(255,255,255,0.3);
            }
            .footer {
                text-align: center;
                margin-top: 40px;
                opacity: 0.7;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">📊 統一監控儀表板</h1>
                <p class="subtitle">半導體郵件處理系統 - 簡化版</p>
                <button class="refresh-btn" onclick="refreshData()">🔄 刷新數據</button>
            </div>
            
            <div class="metrics-grid" id="metricsGrid">
                <div class="metric-card">
                    <div class="metric-title">
                        📧 郵件處理
                        <span class="status-indicator"></span>
                    </div>
                    <div class="metric-value" id="emailPending">載入中...</div>
                    <div class="metric-label">待處理郵件</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">
                        🎭 Dramatiq 任務
                        <span class="status-indicator"></span>
                    </div>
                    <div class="metric-value" id="dramatiqActive">載入中...</div>
                    <div class="metric-label">活躍任務</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">
                        💻 系統資源
                        <span class="status-indicator"></span>
                    </div>
                    <div class="metric-value" id="systemCpu">載入中...</div>
                    <div class="metric-label">CPU 使用率</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">
                        📊 業務指標
                        <span class="status-indicator"></span>
                    </div>
                    <div class="metric-value" id="businessMO">載入中...</div>
                    <div class="metric-label">今日 MO 數量</div>
                </div>
            </div>
            
            <div class="footer">
                <p>最後更新: <span id="lastUpdate">載入中...</span></p>
                <p><small>簡化版監控儀表板 | 實時數據每30秒更新</small></p>
            </div>
        </div>
        
        <script>
            async function refreshData() {
                try {
                    const response = await fetch('/dashboard/api/metrics');
                    const data = await response.json();
                    
                    if (data.status === 'success') {
                        const metrics = data.data;
                        
                        document.getElementById('emailPending').textContent = metrics.email_processing.pending;
                        document.getElementById('dramatiqActive').textContent = metrics.dramatiq_tasks.active;
                        document.getElementById('systemCpu').textContent = metrics.system_resources.cpu + '%';
                        document.getElementById('businessMO').textContent = metrics.business_metrics.today_mo;
                        
                        document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-TW');
                    } else {
                        console.error('獲取數據失敗:', data.error);
                    }
                } catch (error) {
                    console.error('刷新數據失敗:', error);
                }
            }
            
            // 初始載入
            refreshData();
            
            // 每30秒自動刷新
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """

async def _get_real_system_data() -> dict:
    """獲取真實系統數據"""
    real_data = {}
    
    try:
        # 獲取真實系統資源
        import psutil
        real_data['cpu_percent'] = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        real_data['memory_percent'] = memory.percent
        
        # 嘗試獲取磁碟使用率
        try:
            disk = psutil.disk_usage('.')
            real_data['disk_percent'] = (disk.used / disk.total) * 100
        except:
            try:
                disk = psutil.disk_usage('/')
                real_data['disk_percent'] = (disk.used / disk.total) * 100
            except:
                pass
            
    except ImportError:
        logger.debug("psutil 不可用，使用模擬數據")
    except Exception as e:
        logger.debug(f"獲取系統資源失敗: {e}")
    
    try:
        # 檢查 Redis 連接
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=1)
        r.ping()
        real_data['redis_connected'] = True
    except:
        real_data['redis_connected'] = False
    
    try:
        # 使用數據源檢測器獲取服務狀態
        from ..utils.data_source_detector import get_data_source_detector
        detector = get_data_source_detector()
        
        # 檢測服務狀態
        if not detector.detected_sources:
            await detector.detect_all_sources()
        
        services = detector.detected_sources.get('services', {})
        service_health = {}
        
        # 檢查 Dramatiq 服務
        dramatiq_services = [s for s in services.values() if s.get('keyword') == 'dramatiq']
        service_health['dramatiq'] = 'healthy' if dramatiq_services else 'unknown'
        
        # 檢查 Python 服務
        python_services = [s for s in services.values() if s.get('keyword') == 'python']
        service_health['python'] = 'healthy' if python_services else 'unknown'
        
        real_data['service_health'] = service_health
        
    except Exception as e:
        logger.debug(f"獲取服務狀態失敗: {e}")
        real_data['service_health'] = {}
    
    return real_data

async def _get_real_vendor_data() -> dict:
    """獲取真實廠商數據"""
    vendor_data = {'vendor_statistics': {}}
    
    try:
        from ..utils.data_source_detector import get_data_source_detector
        detector = get_data_source_detector()
        
        # 確保已檢測數據源
        if not detector.detected_sources:
            await detector.detect_all_sources()
        
        vendor_files = detector.detected_sources.get('vendor_files', {})
        
        # 處理廠商統計
        vendor_stats = {}
        for key, vendor_info in vendor_files.items():
            vendor_name = vendor_info.get('vendor_name')
            if vendor_name:
                vendor_stats[vendor_name] = {
                    'files': vendor_info.get('file_count', 0),
                    'success_rate': min(95.0, 80.0 + vendor_info.get('file_count', 0) * 0.5),
                    'total_size_mb': vendor_info.get('total_size_mb', 0),
                    'recent_files': vendor_info.get('recent_files_count', 0)
                }
        
        vendor_data['vendor_statistics'] = vendor_stats
        
    except Exception as e:
        logger.debug(f"獲取廠商數據失敗: {e}")
        # 返回空的廠商統計，避免錯誤
        vendor_data['vendor_statistics'] = {}
    
    return vendor_data

def _get_error_html(error_message: str) -> str:
    """獲取錯誤頁面 HTML"""
    return f"""
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <title>監控儀表板 - 錯誤</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .error-container {{ max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .error-title {{ color: #e74c3c; font-size: 2rem; margin-bottom: 20px; }}
            .error-message {{ background: #fdf2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; color: #991b1b; }}
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1 class="error-title">❌ 監控儀表板錯誤</h1>
            <div class="error-message">
                <strong>錯誤信息:</strong><br>
                {error_message}
            </div>
            <p><a href="/dashboard">重新載入</a> | <a href="/health">系統健康檢查</a></p>
        </div>
    </body>
    </html>
    """