#!/usr/bin/env python3
"""
Debug dashboard initialization
"""

import sys
import asyncio
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_dashboard_integrator():
    """測試 dashboard integrator 初始化"""
    try:
        print("🔍 測試導入 dashboard service integrator...")
        from src.dashboard_monitoring.core.dashboard_service_integrator import get_dashboard_integrator
        print("✅ 導入成功")
        
        print("🔍 測試創建實例...")
        integrator = get_dashboard_integrator()
        print("✅ 實例創建成功")
        
        print("🔍 測試創建 FastAPI 應用...")
        from fastapi import FastAPI
        app = FastAPI(title="Test App")
        print("✅ FastAPI 應用創建成功")
        
        print("🔍 測試初始化 dashboard integrator...")
        success = await integrator.initialize(app)
        print(f"{'✅' if success else '❌'} 初始化結果: {success}")
        
        if success:
            print("🔍 檢查註冊的路由...")
            routes = []
            for route in app.routes:
                if hasattr(route, 'path'):
                    if hasattr(route, 'methods'):
                        routes.append(f"{list(route.methods)} {route.path}")
                    else:
                        # WebSocket routes don't have methods
                        routes.append(f"[WebSocket] {route.path}")
            
            dashboard_routes = [r for r in routes if '/dashboard' in r]
            print(f"📋 Dashboard 相關路由 ({len(dashboard_routes)} 個):")
            for route in dashboard_routes:
                print(f"   - {route}")
        
        return success
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_api_import():
    """測試 simple_dashboard_api 導入"""
    try:
        print("\n🔍 測試導入 simple_dashboard_api...")
        from src.dashboard_monitoring.api.simple_dashboard_api import router
        print("✅ simple_dashboard_api 導入成功")
        
        print("🔍 檢查路由器配置...")
        print(f"   - prefix: {router.prefix}")
        print(f"   - tags: {router.tags}")
        
        print("🔍 檢查路由器中的路由...")
        routes = []
        for route in router.routes:
            if hasattr(route, 'path'):
                methods = getattr(route, 'methods', ['UNKNOWN'])
                routes.append(f"{list(methods)} {route.path}")
        
        print(f"📋 API 路由 ({len(routes)} 個):")
        for route in routes:
            print(f"   - {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ simple_dashboard_api 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函數"""
    print("🧪 Dashboard 初始化調試")
    print("=" * 50)
    
    api_success = await test_simple_api_import()
    integrator_success = await test_dashboard_integrator()
    
    print("\n" + "=" * 50)
    print("📊 測試結果:")
    print(f"   Simple API: {'✅ 成功' if api_success else '❌ 失敗'}")
    print(f"   Integrator: {'✅ 成功' if integrator_success else '❌ 失敗'}")
    
    if api_success and integrator_success:
        print("\n💡 所有測試通過，問題可能在主服務的路由註冊過程中")
    else:
        print("\n⚠️ 發現問題，需要修復後再重新啟動服務")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 測試被中斷")
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()