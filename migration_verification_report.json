{"score": 0, "status": "NEEDS FIXES", "recommendation": "Must fix issues before continuing", "errors": ["Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-compose.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_compose.html", "  Blueprint: email, File: js/email-compose.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-ui-utils.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-ui-utils.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-attachments.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-attachments.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-operations.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-operations.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-list-manager.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-list-manager.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-detail.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-detail.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-inbox-core.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\email_detail.html", "  Blueprint: email, File: js/email-inbox-core.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-ui-utils.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-ui-utils.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-attachments.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-attachments.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-operations.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-operations.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-list-manager.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-list-manager.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-detail.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-detail.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-inbox-core.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-inbox-core.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\email\\static\\js\\email-parser-ui.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\email\\templates\\inbox.html", "  Blueprint: email, File: js/email-parser-ui.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\eqc\\static\\js\\compliance.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\eqc\\templates\\compliance.html", "  Blueprint: eqc, File: js/compliance.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\eqc\\static\\js\\eqc-processor.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\eqc\\templates\\eqc_dashboard.html", "  Blueprint: eqc, File: js/eqc-processor.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\eqc\\static\\js\\eqc-dashboard.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\eqc\\templates\\eqc_dashboard.html", "  Blueprint: eqc, File: js/eqc-dashboard.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\eqc\\static\\js\\eqc-history.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\eqc\\templates\\eqc_history.html", "  Blueprint: eqc, File: js/eqc-history.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\file-management\\static\\js\\attachment-browser.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\file-management\\templates\\attachment_browser.html", "  Blueprint: file-management, File: js/attachment-browser.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\file-management\\static\\js\\file-manager.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\file-management\\templates\\file_manager.html", "  Blueprint: file-management, File: js/file-manager.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\file-management\\static\\js\\components\\file-upload.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\file-management\\templates\\upload.html", "  Blueprint: file-management, File: js/components/file-upload.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\monitoring\\static\\js\\health-check.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\monitoring\\templates\\health_check.html", "  Blueprint: monitoring, File: js/health-check.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\monitoring\\static\\js\\system-dashboard.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\monitoring\\templates\\system_dashboard.html", "  Blueprint: monitoring, File: js/system-dashboard.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\tasks\\static\\js\\concurrent-task-manager.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\tasks\\templates\\concurrent_task_manager.html", "  Blueprint: tasks, File: js/concurrent-task-manager.js", "Missing file: D:\\project\\python\\outlook_summary\\frontend\\tasks\\static\\js\\task-dashboard.js", "  Referenced in: D:\\project\\python\\outlook_summary\\frontend\\tasks\\templates\\task_dashboard.html", "  Blueprint: tasks, File: js/task-dashboard.js", "Found 25 missing files"], "warnings": ["JS file has limited functionality: main.js (1 functions/classes)"], "stats": {"css_files": 18, "js_files": 43, "image_files": 8, "template_files": 23, "missing_files": 25, "broken_references": 0}}