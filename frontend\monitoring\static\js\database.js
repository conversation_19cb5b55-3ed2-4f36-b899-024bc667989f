/**
 * 資料庫管理前端邏輯 (核心功能)
 */

class DatabaseManager {
    constructor() {
        this.currentTable = null;
        this.dataTable = null;
        this.selectedRows = new Set();
        this.extensions = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDatabaseInfo();
        // 初始化擴展功能
        this.extensions = new DatabaseManagerExtensions(this);
    }

    bindEvents() {
        // 表格選擇
        $('#table-select').on('change', (e) => {
            const tableName = e.target.value;
            if (tableName) {
                this.loadTableData(tableName);
            } else {
                this.clearTableView();
            }
        });

        // 重新整理按鈕
        $('#refresh-btn').on('click', () => {
            if (this.currentTable) {
                this.loadTableData(this.currentTable);
            }
        });

        // 匯出 CSV 按鈕
        $('#export-csv-btn').on('click', () => {
            if (this.currentTable) {
                this.exportTableToCsv(this.currentTable);
            }
        });

        // 搜尋相關事件
        $('#search-btn').on('click', () => {
            this.performSearch();
        });
        
        $('#clear-search-btn').on('click', () => {
            this.clearSearch();
        });
        
        // 搜尋框 Enter 鍵支援
        $('#search-input').on('keypress', (e) => {
            if (e.which === 13) {
                this.performSearch();
            }
        });

        // 執行查詢按鈕
        $('#execute-query-btn').on('click', () => {
            this.executeQuery();
        });
    }

    async executeQuery() {
        const query = $('#sql-query').val().trim();
        if (!query) {
            this.showError('請輸入查詢語句');
            return;
        }

        this.showLoading();
        $('#query-error').addClass('hidden');

        try {
            const response = await fetch('/api/database/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query })
            });

            const result = await response.json();

            if (result.success) {
                this.displayTableData(result.data);
                $('#export-csv-btn').prop('disabled', true);
            } else {
                $('#query-error').text(result.error).removeClass('hidden');
            }
        } catch (error) {
            console.error('執行查詢失敗:', error);
            $('#query-error').text('執行查詢失敗').removeClass('hidden');
        } finally {
            this.hideLoading();
        }
    }

    async deleteRow(id, tableName) {
        if (!confirm(`確定要刪除這筆記錄嗎？\n\n表格: ${this.getTableDisplayName(tableName)}\nID: ${id}`)) {
            return;
        }
        
        try {
            const response = await fetch(`/api/database/delete/${tableName}/${id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage('記錄已刪除');
                this.loadTableData(tableName);
            } else {
                this.showError('刪除失敗: ' + result.error);
            }
        } catch (error) {
            console.error('刪除記錄失敗:', error);
            this.showError('刪除失敗');
        }
    }

    async loadDatabaseInfo() {
        try {
            const response = await fetch('/api/database/info');
            const result = await response.json();

            if (result.success) {
                const data = result.data;
                // 顯示資料庫大小
                $('#db-size').text(this.formatBytes(data.db_size));
                
                // 顯示表格記錄數
                for (const [table, count] of Object.entries(data.tables)) {
                    $(`#table-select option[value="${table}"]`).text(`${table} - ${this.getTableDisplayName(table)} (${count} 筆記錄)`);
                }
                
                // 自動選擇 emails 表
                $('#table-select').val('emails').trigger('change');
            } else {
                this.showError('載入資料庫資訊失敗');
            }
        } catch (error) {
            console.error('載入資料庫資訊失敗:', error);
            this.showError('載入資料庫資訊失敗');
        }
    }

    async loadTableData(tableName) {
        this.showLoading();
        this.currentTable = tableName;

        try {
            const response = await fetch(`/api/database/table/${tableName}`);
            const result = await response.json();

            if (result.success) {
                this.displayTableData(result.data);
                $('#export-csv-btn').prop('disabled', false);
                this.updateTableInfo(tableName, result.data.records.length, result.data.columns.length);
            } else {
                this.showError('載入表格資料失敗');
            }
        } catch (error) {
            console.error('載入表格資料失敗:', error);
            this.showError('載入表格資料失敗');
        } finally {
            this.hideLoading();
        }
    }

    displayTableData(data) {
        if (this.dataTable) {
            this.dataTable.destroy();
        }

        // 創建表頭
        const thead = this.createTableHeader(data.columns);
        const tbody = this.createTableBody(data.records, data.columns);
        
        $('#data-table').html(thead + tbody);

        // 初始化 DataTable - 使用簡單配置
        this.dataTable = $('#data-table').DataTable({
            pageLength: 25,
            lengthMenu: [10, 25, 50, 100],
            order: [[1, 'desc']], // 預設按第二欄降序
            language: {
                "lengthMenu": "顯示 _MENU_ 筆記錄",
                "zeroRecords": "沒有找到記錄",
                "info": "顯示第 _START_ 到 _END_ 筆記錄，共 _TOTAL_ 筆",
                "infoEmpty": "顯示第 0 到 0 筆記錄，共 0 筆",
                "infoFiltered": "(從 _MAX_ 筆記錄中過濾)",
                "search": "搜尋:",
                "paginate": {
                    "first": "首頁",
                    "last": "末頁",
                    "next": "下一頁",
                    "previous": "上一頁"
                }
            },
            columnDefs: [
                { targets: 0, orderable: false, className: 'select-checkbox' },
                { targets: -1, orderable: false }
            ],
            // 確保顯示分頁控制項
            paging: true,
            pagingType: 'simple_numbers',
            info: true,
            lengthChange: true,
            // 確保所有控制項都顯示
            dom: 'lfrtip'
        });

        // 綁定行選擇事件
        this.bindRowSelectionEvents();
    }





    createTableHeader(columns) {
        let headerHtml = '<thead><tr>';
        headerHtml += '<th><input type="checkbox" id="select-all-rows"></th>';
        
        for (const column of columns) {
            // 隱藏指定欄位
            if (column.name === 'body' || column.name === 'message_id' || 
                column.name === 'is_read' || column.name === 'has_attachments' || 
                column.name === 'attachment_count') {
                continue;
            }
            headerHtml += `<th>${this.getColumnDisplayName(column.name)}</th>`;
        }
        headerHtml += '<th>操作</th></tr></thead>';
        return headerHtml;
    }

    createTableBody(records, columns) {
        let bodyHtml = '<tbody>';
        
        for (const record of records) {
            bodyHtml += '<tr>';
            bodyHtml += `<td><input type="checkbox" class="row-select" data-id="${record.id}"></td>`;
            
            for (const column of columns) {
                // 隱藏指定欄位
                if (column.name === 'body' || column.name === 'message_id' || 
                    column.name === 'is_read' || column.name === 'has_attachments' || 
                    column.name === 'attachment_count') {
                    continue;
                }
                const value = record[column.name];
                const formattedValue = this.formatCellValue(value, column.name);
                const originalValue = value || '';
                
                // 為長內容添加title屬性以支援懸停顯示
                if (this.shouldAddTooltip(column.name, originalValue)) {
                    bodyHtml += `<td title="${this.escapeHtml(originalValue)}">${formattedValue}</td>`;
                } else {
                    bodyHtml += `<td>${formattedValue}</td>`;
                }
            }
            
            bodyHtml += `<td class="action-buttons">
                <button class="btn-view-detail" data-id="${record.id}">查看</button>
                <button class="btn-delete-row" data-id="${record.id}" data-table="${this.currentTable}">刪除</button>
            </td>`;
            bodyHtml += '</tr>';
        }
        
        bodyHtml += '</tbody>';
        return bodyHtml;
    }

    formatCellValue(value, columnName) {
        if (value === null || value === undefined) return 'None';
        if (value === '') return '';

        // 特殊欄位格式化
        if (columnName === 'vendor_code') {
            return this.formatVendorCode(value);
        } else if (columnName === 'parse_status') {
            return this.formatParseStatus(value);
        } else if (columnName === 'extraction_method') {
            return this.formatExtractionMethod(value);
        } else if (columnName.includes('time') || columnName.includes('at')) {
            return this.formatDateTime(value);
        }

        // 處理長文本截斷
        const stringValue = String(value);
        const maxLength = this.getMaxLengthForColumn(columnName);

        if (stringValue.length > maxLength) {
            const truncated = stringValue.substring(0, maxLength) + '...';
            return `<span class="truncated-content" title="${this.escapeHtml(stringValue)}">${this.escapeHtml(truncated)}</span>`;
        }

        return this.escapeHtml(stringValue);
    }

    getMaxLengthForColumn(columnName) {
        // 根據欄位類型設定最大顯示長度
        const lengthMap = {
            'subject': 40,           // 主旨
            'sender': 25,            // 寄件者
            'sender_display_name': 20, // 寄件者名稱
            'pd': 15,                // 產品編號
            'lot': 15,               // 批次編號
            'mo': 15,                // MO編號
            'vendor_code': 10,       // 廠商代碼
            'message_id': 30,        // 郵件ID
            'content': 50            // 內容
        };

        return lengthMap[columnName] || 30; // 預設30字符
    }

    formatVendorCode(value) {
        const colors = {
            'JCET': '#e3f2fd',
            'CTK': '#f3e5f5', 
            'TSM': '#e8f5e9'
        };
        const color = colors[value] || '#f5f5f5';
        return `<span class="vendor-code-tag" style="background-color: ${color}; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">${value}</span>`;
    }

    formatParseStatus(value) {
        const statusMap = {
            'parsed': { text: '已解析', color: '#e8f5e9', textColor: '#4caf50' },
            'failed': { text: '解析失敗', color: '#ffebee', textColor: '#f44336' },
            'pending': { text: '待解析', color: '#fff3e0', textColor: '#ff9800' }
        };
        const status = statusMap[value] || { text: value, color: '#f5f5f5', textColor: '#666' };
        return `<span class="parse-status-tag" style="background-color: ${status.color}; color: ${status.textColor}; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">${status.text}</span>`;
    }

    formatExtractionMethod(value) {
        const methodMap = {
            'llm': { text: 'LLM', color: '#e3f2fd', textColor: '#1976d2' },
            'traditional': { text: '傳統', color: '#e8f5e9', textColor: '#388e3c' },
            'fallback': { text: '備援', color: '#fff3e0', textColor: '#f57c00' }
        };
        const method = methodMap[value] || { text: value || 'None', color: '#f5f5f5', textColor: '#666' };
        return `<span class="extraction-method-tag" style="background-color: ${method.color}; color: ${method.textColor}; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">${method.text}</span>`;
    }

    formatDateTime(value) {
        if (!value || value === 'None') return 'None';
        try {
            return new Date(value).toLocaleString('zh-TW');
        } catch {
            return value;
        }
    }

    bindRowSelectionEvents() {
        // 行選擇變化事件
        $('#data-table').on('change', '.row-select', () => {
            this.updateSelectionUI();
        });

        // 全選/取消全選
        $('#select-all-rows').on('change', (e) => {
            $('.row-select').prop('checked', e.target.checked);
            this.updateSelectionUI();
        });

        // 查看詳情按鈕
        $('#data-table').on('click', '.btn-view-detail', (e) => {
            const id = $(e.target).data('id');
            this.showRecordDetail(id);
        });

        // 刪除按鈕
        $('#data-table').on('click', '.btn-delete-row', (e) => {
            const id = $(e.target).data('id');
            const table = $(e.target).data('table');
            this.deleteRow(id, table);
        });
    }

    updateSelectionUI() {
        const selectedCount = $('.row-select:checked').length;
        
        if (selectedCount > 0) {
            this.showBatchActionsPanel(selectedCount);
        } else {
            this.hideBatchActionsPanel();
        }
    }

    showBatchActionsPanel(selectedCount) {
        $('.batch-actions-panel').remove();
        
        const panel = $(`
            <div class="batch-actions-panel">
                <div class="batch-info">
                    已選取 <strong>${selectedCount}</strong> 筆記錄
                </div>
                <div class="batch-actions">
                    <button id="batch-delete-btn" class="btn btn-danger">
                        🗑️ 刪除選取項目
                    </button>
                    <button id="clear-selection-btn" class="btn btn-secondary">
                        ❌ 清除選取
                    </button>
                </div>
            </div>
        `);
        
        panel.css({
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: '#fff',
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '15px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: '15px'
        });
        
        $('body').append(panel);
        
        // 綁定事件
        $('#batch-delete-btn').on('click', () => {
            this.batchDelete();
        });
        
        $('#clear-selection-btn').on('click', () => {
            this.clearSelection();
        });
    }

    hideBatchActionsPanel() {
        $('.batch-actions-panel').remove();
    }

    clearSelection() {
        $('.row-select').prop('checked', false);
        $('#select-all-rows').prop('checked', false);
        this.hideBatchActionsPanel();
    }

    async batchDelete() {
        const selectedIds = [];
        $('.row-select:checked').each((index, element) => {
            selectedIds.push($(element).attr('data-id'));
        });
        
        if (selectedIds.length === 0) {
            this.showError('請選取要刪除的記錄');
            return;
        }
        
        const tableName = this.currentTable;
        const confirmMessage = `確定要刪除這 ${selectedIds.length} 筆記錄嗎？\n\n表格: ${this.getTableDisplayName(tableName)}\n\n此操作無法復原！`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        this.showLoading();
        
        try {
            const response = await fetch(`/api/database/batch-delete/${tableName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ids: selectedIds })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage(`成功刪除 ${selectedIds.length} 筆記錄`);
                this.hideBatchActionsPanel();
                this.loadTableData(tableName);
            } else {
                this.showError('批量刪除失敗: ' + result.error);
            }
        } catch (error) {
            console.error('批量刪除失敗:', error);
            this.showError('批量刪除失敗');
        } finally {
            this.hideLoading();
        }
    }

    async performSearch() {
        const searchTerm = $('#search-input').val().trim();
        if (!searchTerm) {
            this.clearSearch();
            return;
        }

        if (this.dataTable) {
            this.dataTable.search(searchTerm).draw();
            $('#search-results-info').removeClass('hidden');
            $('#search-results-text').text(`搜尋「${searchTerm}」的結果`);
        }
    }

    clearSearch() {
        $('#search-input').val('');
        if (this.dataTable) {
            this.dataTable.search('').draw();
        }
        $('#search-results-info').addClass('hidden');
        
        // 清除進階搜尋篩選
        if (this.extensions) {
            this.extensions.resetAdvancedFilters();
        }
        
        // 重置全選按鈕狀態
        $('#select-all-search-btn').html('☑️ 全選搜尋結果');
        this.hideBatchActionsPanel();
    }

    async showRecordDetail(id) {
        try {
            const response = await fetch(`/api/database/${this.currentTable}/${id}`);
            const result = await response.json();

            if (result.success) {
                this.displayRecordDetail(result.data);
            } else {
                this.showError('載入記錄詳情失敗');
            }
        } catch (error) {
            console.error('載入記錄詳情失敗:', error);
            this.showError('載入記錄詳情失敗');
        }
    }

    displayRecordDetail(record) {
        const modalBody = $('#modal-body');
        let detailHtml = '<div class="record-detail">';
        
        for (const [key, value] of Object.entries(record)) {
            detailHtml += `
                <div class="detail-row">
                    <strong>${this.getColumnDisplayName(key)}:</strong>
                    <span>${this.formatCellValue(value, key)}</span>
                </div>
            `;
        }
        
        detailHtml += '</div>';
        modalBody.html(detailHtml);
        $('#modal-title').text('記錄詳情');
        $('#detail-modal').removeClass('hidden');
    }

    updateTableInfo(tableName, recordCount, columnCount) {
        $('#table-name').text(`${tableName} - ${this.getTableDisplayName(tableName)}`);
        $('#record-count').text(recordCount);
        $('#column-count').text(columnCount);
        $('#table-info').removeClass('hidden');
    }

    exportTableToCsv(tableName) {
        window.location.href = `/api/database/export/${tableName}`;
    }

    clearTableView() {
        if (this.dataTable) {
            this.dataTable.destroy();
            $('#data-table').empty();
        }
        $('#table-info').addClass('hidden');
        $('#export-csv-btn').prop('disabled', true);
        this.currentTable = null;
    }

    showLoading() {
        $('#loading').removeClass('hidden');
    }

    hideLoading() {
        $('#loading').addClass('hidden');
    }

    showError(message) {
        alert(message);
    }

    showSuccessMessage(message) {
        $('.success-message').remove();
        
        const messageDiv = $(`
            <div class="success-message" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 4px;
                z-index: 9999;
                font-weight: 500;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            ">
                ${message}
            </div>
        `);
        
        $('body').append(messageDiv);
        
        setTimeout(() => {
            messageDiv.fadeOut(() => messageDiv.remove());
        }, 3000);
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    escapeHtml(text) {
        if (text === null || text === undefined) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    getTableDisplayName(tableName) {
        const names = {
            'emails': '郵件',
            'senders': '寄件者',
            'attachments': '附件',
            'email_process_status': '處理狀態'
        };
        return names[tableName] || tableName;
    }

    shouldAddTooltip(columnName, value) {
        // 為長內容欄位添加懸停提示
        const longContentColumns = [
            'sender', 'subject', 'lot', 'mo', 'parse_error', 
            'llm_analysis_result', 'llm_analysis_timestamp'
        ];
        
        if (!longContentColumns.includes(columnName)) {
            return false;
        }
        
        // 如果內容長度超過30個字符或包含JSON格式，就添加tooltip
        const stringValue = String(value);
        return stringValue.length > 30 || stringValue.includes('{') || stringValue.includes('[');
    }

    getColumnDisplayName(columnName) {
        const names = {
            'id': 'ID',
            'message_id': '郵件 ID',
            'sender': '寄件者',
            'sender_display_name': '寄件者名稱',
            'subject': '主旨',
            'received_time': '接收時間',
            'created_at': '創建時間',
            'pd': '產品編號',
            'lot': '批次編號',
            'mo': 'MO (製造訂單)',
            'vendor_code': '廠商代碼',
            'parse_status': '解析狀態',
            'extraction_method': '解析方法'
        };
        return names[columnName] || columnName;
    }
}

// 關閉模態框函數
function closeDetailModal() {
    $('#detail-modal').addClass('hidden');
}

// 初始化
$(document).ready(() => {
    window.databaseManager = new DatabaseManager();
});

// 全域函數 - 常用查詢範例
function setQuery(query) {
    $('#sql-query').val(query);
    // 顯示SQL查詢區域
    $('#sql-query').parent().show();
    // 自動執行查詢
    $('#execute-query-btn').click();
}