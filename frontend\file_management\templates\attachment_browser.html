<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>附件瀏覽器 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('files.static', filename='css/file-manager.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="attachment-browser-container">
        <header class="browser-header">
            <h1>附件瀏覽器</h1>
            <div class="header-stats">
                <span class="stat-item">總附件: <strong id="total-attachments">{{ stats.total_attachments or 0 }}</strong></span>
                <span class="stat-item">總大小: <strong id="total-size">{{ stats.total_size_formatted or '0 MB' }}</strong></span>
                <span class="stat-item">檔案類型: <strong id="file-types-count">{{ stats.file_types_count or 0 }}</strong></span>
            </div>
            <div class="header-actions">
                <button id="refresh-attachments-btn" class="btn btn-primary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
                <button id="batch-download-btn" class="btn btn-secondary" disabled>
                    <span class="btn-icon">📥</span>
                    <span class="btn-text">批次下載</span>
                </button>
                <a href="{{ url_for('file-management.file_manager') }}" class="btn btn-outline">
                    <span class="btn-icon">🗂️</span>
                    <span class="btn-text">檔案管理器</span>
                </a>
            </div>
        </header>

        <div class="browser-content">
            <!-- 篩選和搜尋區 -->
            <div class="filter-section">
                <div class="filter-card">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="search-attachments">搜尋附件:</label>
                            <div class="search-input">
                                <input type="text" id="search-attachments" placeholder="輸入檔案名稱、寄件者或主旨...">
                                <button id="search-btn" class="btn btn-sm btn-primary">搜尋</button>
                                <button id="clear-search-btn" class="btn btn-sm btn-outline">清除</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="file-type-filter">檔案類型:</label>
                            <select id="file-type-filter">
                                <option value="all" selected>全部類型</option>
                                <option value="pdf">PDF 文件</option>
                                <option value="excel">Excel 試算表</option>
                                <option value="word">Word 文件</option>
                                <option value="powerpoint">PowerPoint 簡報</option>
                                <option value="image">圖片檔案</option>
                                <option value="archive">壓縮檔案</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="size-filter">檔案大小:</label>
                            <select id="size-filter">
                                <option value="all" selected>全部大小</option>
                                <option value="small">小於 1MB</option>
                                <option value="medium">1MB - 10MB</option>
                                <option value="large">10MB - 50MB</option>
                                <option value="huge">大於 50MB</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="date-filter">收到日期:</label>
                            <select id="date-filter">
                                <option value="all" selected>全部日期</option>
                                <option value="today">今天</option>
                                <option value="week">本週</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季</option>
                                <option value="year">今年</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="sender-filter">寄件者:</label>
                            <select id="sender-filter">
                                <option value="all" selected>全部寄件者</option>
                                {% for sender in top_senders %}
                                <option value="{{ sender.email }}">{{ sender.name }} ({{ sender.count }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button id="apply-filters-btn" class="btn btn-primary">套用篩選</button>
                        <button id="reset-filters-btn" class="btn btn-outline">重設</button>
                    </div>
                </div>
            </div>

            <!-- 統計圖表區 -->
            <div class="stats-section">
                <div class="stats-row">
                    <div class="stat-chart-card">
                        <h3>檔案類型分佈</h3>
                        <div class="chart-container">
                            <canvas id="file-type-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="stat-chart-card">
                        <h3>檔案大小分佈</h3>
                        <div class="chart-container">
                            <canvas id="file-size-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="stat-chart-card">
                        <h3>每月附件趨勢</h3>
                        <div class="chart-container">
                            <canvas id="monthly-trend-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 附件列表區 -->
            <div class="attachment-list-section">
                <div class="list-header">
                    <div class="list-controls">
                        <div class="view-controls">
                            <button class="view-btn active" data-view="list">📋 列表</button>
                            <button class="view-btn" data-view="grid">⊞ 網格</button>
                        </div>
                        <div class="sort-controls">
                            <label for="sort-by">排序:</label>
                            <select id="sort-by">
                                <option value="received_date" selected>收到日期</option>
                                <option value="filename">檔案名稱</option>
                                <option value="size">檔案大小</option>
                                <option value="sender">寄件者</option>
                                <option value="file_type">檔案類型</option>
                            </select>
                            <button id="sort-direction-btn" class="btn btn-sm btn-outline" data-direction="desc">↓</button>
                        </div>
                        <div class="selection-info">
                            <span id="shown-count">顯示 {{ attachments|length }} / {{ stats.total_attachments }} 個附件</span>
                            <span id="selected-info" style="display: none;"></span>
                        </div>
                    </div>
                </div>

                <!-- 列表視圖 -->
                <div class="attachment-list-view active" id="list-view">
                    <table class="attachment-table">
                        <thead>
                            <tr>
                                <th class="select-column">
                                    <input type="checkbox" id="select-all-attachments">
                                </th>
                                <th class="filename-column">檔案名稱</th>
                                <th class="size-column">大小</th>
                                <th class="sender-column">寄件者</th>
                                <th class="subject-column">郵件主旨</th>
                                <th class="date-column">收到日期</th>
                                <th class="status-column">狀態</th>
                                <th class="actions-column">操作</th>
                            </tr>
                        </thead>
                        <tbody id="attachment-table-body">
                            {% for attachment in attachments %}
                            <tr class="attachment-row" data-id="{{ attachment.id }}" data-type="{{ attachment.file_type }}">
                                <td>
                                    <input type="checkbox" class="attachment-select" value="{{ attachment.id }}">
                                </td>
                                <td class="filename-cell">
                                    <div class="file-info">
                                        <span class="file-icon">{{ attachment.icon }}</span>
                                        <div class="file-details">
                                            <span class="file-name" title="{{ attachment.filename }}">{{ attachment.filename }}</span>
                                            <small class="file-path">{{ attachment.path }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="size-cell">{{ attachment.size_formatted }}</td>
                                <td class="sender-cell">
                                    <div class="sender-info">
                                        <span class="sender-name">{{ attachment.sender_name }}</span>
                                        <small class="sender-email">{{ attachment.sender_email }}</small>
                                    </div>
                                </td>
                                <td class="subject-cell">
                                    <span class="email-subject" title="{{ attachment.email_subject }}">
                                        {{ attachment.email_subject|truncate(50) }}
                                    </span>
                                </td>
                                <td class="date-cell">{{ attachment.received_date.strftime('%Y-%m-%d %H:%M') if attachment.received_date else 'N/A' }}</td>
                                <td class="status-cell">
                                    <span class="status-badge {{ attachment.status }}">
                                        {% if attachment.status == 'downloaded' %}✅ 已下載
                                        {% elif attachment.status == 'processed' %}🔄 已處理
                                        {% elif attachment.status == 'pending' %}⏳ 待處理
                                        {% elif attachment.status == 'error' %}❌ 錯誤
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="actions-cell">
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary" onclick="downloadAttachment('{{ attachment.id }}')">
                                            <span class="btn-icon">💾</span>
                                        </button>
                                        <button class="btn btn-sm btn-secondary" onclick="previewAttachment('{{ attachment.id }}')">
                                            <span class="btn-icon">👁️</span>
                                        </button>
                                        <button class="btn btn-sm btn-outline" onclick="openEmailDetail('{{ attachment.email_id }}')">
                                            <span class="btn-icon">📧</span>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline dropdown-toggle">⋮</button>
                                            <div class="dropdown-menu">
                                                <a href="#" onclick="processAttachment('{{ attachment.id }}')">處理檔案</a>
                                                <a href="#" onclick="moveAttachment('{{ attachment.id }}')">移動到...</a>
                                                <a href="#" onclick="copyAttachment('{{ attachment.id }}')">複製到...</a>
                                                <div class="dropdown-divider"></div>
                                                <a href="#" onclick="deleteAttachment('{{ attachment.id }}')">刪除</a>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 網格視圖 -->
                <div class="attachment-grid-view" id="grid-view">
                    <div class="attachment-grid">
                        {% for attachment in attachments %}
                        <div class="attachment-card" data-id="{{ attachment.id }}" data-type="{{ attachment.file_type }}">
                            <div class="card-select">
                                <input type="checkbox" class="attachment-select" value="{{ attachment.id }}">
                            </div>
                            <div class="card-preview">
                                {% if attachment.file_type == 'image' %}
                                <img src="{{ attachment.thumbnail_url }}" alt="{{ attachment.filename }}" loading="lazy">
                                {% else %}
                                <div class="file-icon-large">{{ attachment.icon }}</div>
                                {% endif %}
                            </div>
                            <div class="card-content">
                                <div class="card-title" title="{{ attachment.filename }}">{{ attachment.filename }}</div>
                                <div class="card-meta">
                                    <div class="card-size">{{ attachment.size_formatted }}</div>
                                    <div class="card-date">{{ attachment.received_date.strftime('%Y-%m-%d') if attachment.received_date else 'N/A' }}</div>
                                </div>
                                <div class="card-sender">來自: {{ attachment.sender_name }}</div>
                                <div class="card-status">
                                    <span class="status-badge {{ attachment.status }}">
                                        {% if attachment.status == 'downloaded' %}✅
                                        {% elif attachment.status == 'processed' %}🔄
                                        {% elif attachment.status == 'pending' %}⏳
                                        {% elif attachment.status == 'error' %}❌
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary" onclick="downloadAttachment('{{ attachment.id }}')">下載</button>
                                <button class="btn btn-sm btn-secondary" onclick="previewAttachment('{{ attachment.id }}')">預覽</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 分頁控制 -->
                <div class="pagination" id="attachment-pagination">
                    <!-- 分頁控制會由JavaScript動態生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 附件預覽模態框 -->
    <div class="modal" id="attachment-preview-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="preview-attachment-title">附件預覽</h3>
                <div class="preview-actions">
                    <button id="download-preview-attachment-btn" class="btn btn-sm btn-primary">下載</button>
                    <button id="process-preview-attachment-btn" class="btn btn-sm btn-secondary">處理</button>
                </div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="preview-info">
                    <div class="info-row">
                        <span class="info-label">檔案名稱:</span>
                        <span class="info-value" id="preview-filename"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">檔案大小:</span>
                        <span class="info-value" id="preview-filesize"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">寄件者:</span>
                        <span class="info-value" id="preview-sender"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">郵件主旨:</span>
                        <span class="info-value" id="preview-email-subject"></span>
                    </div>
                </div>
                <div id="attachment-preview-content">
                    <!-- 預覽內容會由JavaScript動態載入 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('attachment-preview-modal')">關閉</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('files.static', filename='js/attachment-browser.js') }}"></script>
</body>
</html>