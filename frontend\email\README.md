# Email Module - 郵件功能模組

## 概述

郵件功能模組負責處理半導體郵件處理系統中的所有郵件相關功能，包括收件匣管理、郵件詳情查看、郵件撰寫和郵件設定。

## 功能特性

### 核心功能
- **收件匣管理** - 顯示和管理收到的郵件列表
- **郵件詳情** - 查看郵件內容、附件和相關資訊
- **郵件撰寫** - 撰寫和發送新郵件
- **郵件設定** - 配置郵件帳戶和處理規則

### 廠商支援
- **ETD** - `anf` 關鍵字識別
- **GTK** - `ft hold`, `ft lot` 關鍵字識別
- **JCET** - `jcet` 關鍵字識別
- **LINGSEN** - `lingsen` 關鍵字識別
- **XAHT** - `tianshui`, `西安` 關鍵字識別

## 目錄結構

```
email/
├── templates/                # HTML 模板
│   ├── inbox.html           # 收件匣頁面
│   ├── email_detail.html    # 郵件詳情頁面
│   ├── email_compose.html   # 撰寫郵件頁面
│   └── email_settings.html  # 郵件設定頁面
├── static/                  # 靜態資源
│   ├── css/
│   │   └── email.css        # 郵件專用樣式
│   ├── js/
│   │   ├── email-list.js    # 郵件列表邏輯
│   │   ├── email-detail.js  # 郵件詳情邏輯
│   │   └── email-api.js     # 郵件 API 調用
│   └── images/              # 郵件相關圖片
├── components/              # 可重用組件
│   ├── email-card.html      # 郵件卡片組件
│   ├── attachment-viewer.html # 附件查看器
│   └── email-toolbar.html   # 郵件工具列
├── routes/                  # 路由處理
│   └── email_routes.py      # 郵件相關路由
└── README.md                # 本檔案
```

## API 端點

### 郵件管理
- `GET /email/inbox` - 收件匣頁面
- `GET /email/detail/<id>` - 郵件詳情頁面
- `GET /api/email/list` - 獲取郵件列表
- `GET /api/email/<id>` - 獲取郵件詳情
- `POST /api/email/mark-read` - 標記郵件為已讀
- `DELETE /api/email/<id>` - 刪除郵件

### 附件管理
- `GET /api/email/<id>/attachments` - 獲取郵件附件列表
- `GET /api/email/attachment/<id>/download` - 下載附件

## 資料模型

### Email
- `id`: 郵件唯一識別碼
- `subject`: 郵件主旨
- `sender`: 寄件者
- `recipient`: 收件者
- `content`: 郵件內容
- `attachments`: 附件列表
- `is_read`: 是否已讀
- `vendor_type`: 廠商類型
- `created_at`: 建立時間
- `updated_at`: 更新時間

### EmailAttachment
- `id`: 附件唯一識別碼
- `filename`: 檔案名稱
- `size`: 檔案大小
- `content_type`: 檔案類型
- `download_url`: 下載連結

## 開發注意事項

- 遵循現有的郵件處理邏輯
- 保持與後端 Flask 服務 (Port 5000) 的 API 相容性
- 支援即時郵件更新 (WebSocket)
- 實作適當的錯誤處理和載入狀態
- 確保附件安全下載和預覽功能