# Frontend - 前端開發指南

## 概述

本目錄包含半導體郵件處理系統的前端代碼，採用模組化架構設計，為未來遷移到 Vue.js 做準備。

## 目錄結構

```
frontend/
├── email/                    # 郵件功能模組
├── analytics/                # 分析統計功能模組
├── file-management/          # 檔案管理功能模組
├── eqc/                      # EQC功能模組
├── tasks/                    # 任務管理功能模組
├── monitoring/               # 監控功能模組
├── shared/                   # 共享前端資源
├── app.py                    # Flask 主應用程式
├── config.py                 # Flask 配置
└── README.md                 # 本檔案
```

## 功能模組

### 六大功能領域

1. **Email (郵件)** - 郵件收件匣管理、郵件詳情查看、郵件設定
2. **Analytics (分析統計)** - 統計儀表板、報表生成、廠商分析
3. **File Management (檔案管理)** - 檔案上傳、檔案瀏覽、附件管理
4. **EQC (設備品質控制)** - EQC儀表板、品質檢查、合規檢查
5. **Tasks (任務管理)** - 任務儀表板、任務隊列、任務調度
6. **Monitoring (監控)** - 系統監控、健康檢查、效能指標

### 共享資源

- **Templates** - 基礎模板、佈局組件、共享組件
- **Static Assets** - 全域樣式、共用JavaScript、第三方函式庫
- **Utilities** - 工具函數、API客戶端、常數定義

## 開發指南

### 模組結構

每個功能模組都遵循相同的目錄結構：

```
module_name/
├── templates/                # HTML 模板
├── static/                   # 靜態資源
│   ├── css/                  # 模組專用樣式
│   ├── js/                   # 模組專用JavaScript
│   └── images/               # 模組專用圖片
├── components/               # 可重用組件
├── routes/                   # 路由處理
└── README.md                 # 模組說明
```

### 技術堆疊

- **Flask 2.3.3** - Web 框架
- **Jinja2** - 模板引擎
- **HTML5 + CSS3 + JavaScript ES6** - 前端技術
- **Bootstrap** - CSS 框架
- **Chart.js** - 圖表庫
- **jQuery** - DOM 操作

## 開發環境設定

### 快速啟動

1. **啟動虛擬環境**:
   ```bash
   # Windows
   . .\dev_env.ps1
   
   # 或手動啟動
   .\venv_win_3_11_12\Scripts\activate
   ```

2. **設定環境變數**:
   ```bash
   # 複製環境變數模板
   cp .env.example .env
   
   # 編輯 .env 檔案設定必要參數
   ```

3. **啟動應用程式**:
   ```bash
   # 方式1: 直接執行
   python frontend/app.py
   
   # 方式2: 使用 Flask CLI
   flask run
   
   # 方式3: 使用自訂 CLI
   python frontend/cli.py run --config development
   
   # 方式4: 使用 Makefile
   make run-frontend
   ```

### 配置管理

**多環境支援**:
- **開發環境**: `FLASK_ENV=development` (預設)
- **測試環境**: `FLASK_ENV=testing`
- **生產環境**: `FLASK_ENV=production`

**重要配置項目**:
```bash
# Flask 應用程式配置
FLASK_APP=frontend.app:create_app
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# 安全配置
SECRET_KEY=your_secret_key_here

# 檔案上傳配置
UPLOAD_FOLDER=temp/uploads
MAX_CONTENT_LENGTH=16777216
```

### 開發工具

**Flask CLI 管理工具**:
```bash
# 檢查應用程式配置
python frontend/cli.py check-config

# 測試所有模組載入
python frontend/cli.py test-modules

# 創建必要目錄
python frontend/cli.py create-dirs

# 靈活啟動選項
python frontend/cli.py run --config production --port 8000
```

**Makefile 命令**:
```bash
# 啟動前端應用程式
make run-frontend

# 使用 Flask CLI 啟動
make run-flask

# 生產模式啟動
make run-flask-prod

# 啟動整合服務
make run-services
```

## 架構特色

### 工廠模式
- 使用 `create_app()` 工廠函數支援多環境配置
- 靈活的應用程式初始化和配置管理
- 支援測試環境的獨立配置

### 模組化靜態資源
- 每個模組擁有獨立的靜態資源路由
- 避免模組間資源衝突
- 支援模組級資源版本控制

### 藍圖系統
- 每個功能模組使用獨立的 Flask 藍圖
- URL 前綴隔離 (`/email/`, `/analytics/` 等)
- 清晰的模組邊界和職責分離

## 注意事項

- **當前階段**: Flask 模組化重構已完成 ✅
- **技術堆疊**: Flask + HTML/CSS/JavaScript (準備 Vue.js 遷移)
- **架構原則**: 遵循 hexagonal architecture 和模組化設計
- **向後兼容**: 保持所有現有功能和 URL 路徑不變
- **Vue.js 準備**: 建立清晰的模組邊界，為前端框架遷移奠定基礎

## 最新更新 (2025-08-11)

### 任務 5.1 完成 ✅
- **Flask 配置現代化**: 實作工廠模式和多環境支援
- **靜態資源管理**: 解決模組間路由衝突，實作獨立資源路由
- **開發工具增強**: 新增 CLI 管理工具和開發腳本
- **生產環境準備**: 完整的安全配置和效能優化設定

**驗證結果**: 所有 6 個模組 100% 正常載入，配置系統完全正常運作