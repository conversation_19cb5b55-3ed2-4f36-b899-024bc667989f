# 測試目錄結構說明

## 📁 目錄分類

### `/api` - API測試
- API端點測試
- 直接API調用測試
- 特定端點功能測試

### `/dependency_injection` - 依賴注入測試
- 依賴注入系統測試
- 服務容器測試
- 階段性重構測試

### `/docs` - 測試文檔
- 測試指南
- 測試結果總結
- 並發測試指南

### `/dramatiq` - Dramatiq任務測試
- Dramatiq集成測試
- 並發任務測試
- EQC任務測試

### `/e2e` - 端到端測試
- 完整工作流程測試
- UI集成測試
- 下載修復測試

### `/fixtures` - 測試固定數據
- 測試數據樣本
- 預期結果
- 資料夾結構樣本

### `/fixes` - 修復功能測試 🆕
- `test_fixes_verification.py` - 修復效果驗證
- `test_pd_vendor_fix.py` - PD/廠商代碼修復測試
- `test_notification_dedup.py` - 通知去重測試
- `test_retry_fixes.py` - 重試機制修復測試

### `/integration` - 集成測試
- 儀表板集成測試
- 檔案處理集成測試
- 管道系統測試
- LLM搜尋API測試

### `/load` - 負載測試
- 多用戶負載測試
- 記憶體模式測試
- 生產環境負載測試

### `/monitoring` - 監控測試
- 錯誤處理測試
- 監控功能測試
- 真實錯誤場景測試

### `/performance` - 性能測試
- 異步性能測試
- 集成基準測試
- WebSocket性能測試

### `/phase4_testing_validation` - 第四階段測試驗證
- 全面測試驗證
- 依賴覆蓋測試
- 性能驗證測試

### `/playwright` - Playwright測試
- 多用戶並發測試
- EQC Dramatiq Worker測試
- 瀏覽器自動化測試

### `/scenarios` - 場景測試 🆕
- `test_actual_scenario.py` - 實際使用場景測試
- `test_complete_workflow.py` - 完整工作流程測試

### `/system` - 系統測試
- 最小系統測試
- 生產環境測試

### `/unit` - 單元測試
- 應用層單元測試
- 數據模型測試
- 基礎設施測試
- 表現層測試

### `/vendors` - 廠商相關測試 🆕
- `test_etd_email_processing.py` - ETD郵件處理測試
- `test_vendor_file_processing.py` - 廠商檔案處理測試

## 🚀 運行測試

### 運行所有測試
```bash
pytest
```

### 運行特定分類測試
```bash
# 修復功能測試
pytest tests/fixes/

# 廠商相關測試
pytest tests/vendors/

# 場景測試
pytest tests/scenarios/

# 單元測試
pytest tests/unit/

# 集成測試
pytest tests/integration/
```

### 運行特定測試文件
```bash
# 修復驗證測試
pytest tests/fixes/test_fixes_verification.py

# ETD郵件處理測試
pytest tests/vendors/test_etd_email_processing.py

# 實際場景測試
pytest tests/scenarios/test_actual_scenario.py
```

## 📋 測試命名規範

- `test_*.py` - 測試文件
- `test_*()` - 測試函數
- `*_test.py` - 替代測試文件命名
- `conftest.py` - pytest配置文件

## 🔧 測試工具

- **pytest** - 主要測試框架
- **playwright** - 瀏覽器自動化測試
- **asyncio** - 異步測試支援
- **fixtures** - 測試數據固定裝置

## 📝 新增測試指南

1. **選擇適當分類**: 根據測試性質選擇對應目錄
2. **遵循命名規範**: 使用 `test_` 前綴
3. **添加文檔**: 在測試文件中添加清楚的說明
4. **更新README**: 如有新分類，更新此文檔

## 🎯 測試覆蓋目標

- ✅ 修復功能驗證
- ✅ 廠商特定功能
- ✅ 實際使用場景
- ✅ API端點測試
- ✅ 集成測試
- ✅ 性能測試
- ✅ 錯誤處理測試
