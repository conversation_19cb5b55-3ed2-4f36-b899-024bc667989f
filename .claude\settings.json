{"hooks": {"PostToolUse": [{"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "python D:/project/python/outlook_summary/.claude/run_hooks.py --analyze", "timeout": 300}]}], "Stop": [{"hooks": [{"type": "command", "command": "python D:/project/python/outlook_summary/.claude/run_hooks.py --analyze", "run_in_background": true}, {"type": "command", "command": "python D:/project/python/outlook_summary/.claude/hooks/update_docs_safe.py", "run_in_background": true}]}]}, "env": {"DISABLE_AUTOUPDATER": "1"}, "statusLine": {"type": "command", "command": "powershell -Command \"$input = $input | ConvertFrom-Json; $model = $input.model.display_name; $dir = $input.workspace.current_dir; $ver = $input.version; try { $branch = git -C $dir branch --show-current 2>$null; if (-not $branch) { $branch = '分離HEAD' }; $isDirty = git -C $dir diff-index --quiet HEAD -- 2>$null; if ($LASTEXITCODE -ne 0) { $branch += '*' }; $git = $branch } catch { $git = '非Git倉庫' }; $shortDir = if ($dir.Length -gt 50) { '...' + $dir.Substring($dir.Length - 47) } else { $dir }; Write-Output ('{0} | {1} | {2} | v{3}' -f $model, $git, $shortDir, $ver)\""}}