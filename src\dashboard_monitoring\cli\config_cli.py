#!/usr/bin/env python3
"""
統一監控儀表板 - 配置管理命令行工具

提供配置驗證、備份、恢復和管理的命令行介面
"""

import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any

# 添加專案根目錄到路徑
sys.path.append(str(Path(__file__).parent.parent.parent))

from dashboard_monitoring.config.dashboard_config import DashboardConfig, get_dashboard_config
from dashboard_monitoring.utils.config_manager import ConfigManager


def validate_command(args) -> None:
    """驗證配置命令"""
    print("🔍 驗證儀表板配置...")
    
    try:
        config_manager = ConfigManager()
        validation_result = config_manager.validate_config()
        
        if validation_result["is_valid"]:
            print("✅ 配置驗證通過")
        else:
            print("❌ 配置驗證失敗")
            for error in validation_result["errors"]:
                print(f"   錯誤: {error}")
        
        if validation_result["warnings"]:
            print("⚠️  配置警告:")
            for warning in validation_result["warnings"]:
                print(f"   警告: {warning}")
        
        if args.verbose:
            print("\n📊 配置摘要:")
            summary = validation_result["config_summary"]
            print(f"   指標更新間隔: {summary['metrics_update_interval']} 秒")
            print(f"   告警檢查間隔: {summary['alerts_check_interval']} 秒")
            print(f"   郵件待處理閾值: 警告={summary['email_pending_thresholds']['warning']}, 嚴重={summary['email_pending_thresholds']['critical']}")
            print(f"   Dramatiq待處理閾值: 警告={summary['dramatiq_pending_thresholds']['warning']}, 嚴重={summary['dramatiq_pending_thresholds']['critical']}")
            print(f"   CPU使用率閾值: 警告={summary['system_resource_thresholds']['cpu_warning']}%, 嚴重={summary['system_resource_thresholds']['cpu_critical']}%")
            print(f"   記憶體使用率閾值: 警告={summary['system_resource_thresholds']['memory_warning']}%, 嚴重={summary['system_resource_thresholds']['memory_critical']}%")
            print(f"   資料保留天數: 指標={summary['retention_days']['metrics']}, 告警={summary['retention_days']['alerts']}")
            print(f"   WebSocket最大連接數: {summary['websocket_max_connections']}")
            print(f"   資料庫路徑: {summary['database_path']}")
        
    except Exception as e:
        print(f"❌ 配置驗證失敗: {e}")
        sys.exit(1)


def backup_command(args) -> None:
    """備份配置命令"""
    print("💾 備份儀表板配置...")
    
    try:
        config_manager = ConfigManager()
        backup_file = config_manager.backup_config(backup_name=args.name)
        print(f"✅ 配置已備份到: {backup_file}")
        
    except Exception as e:
        print(f"❌ 配置備份失敗: {e}")
        sys.exit(1)


def restore_command(args) -> None:
    """恢復配置命令"""
    print(f"🔄 從備份恢復配置: {args.backup_file}")
    
    try:
        config_manager = ConfigManager()
        backup_file = Path(args.backup_file)
        
        if not backup_file.exists():
            print(f"❌ 備份檔案不存在: {backup_file}")
            sys.exit(1)
        
        config = config_manager.restore_config(backup_file)
        print(f"✅ 配置已從備份恢復")
        
        # 驗證恢復的配置
        validation_result = config_manager.validate_config(config)
        if validation_result["is_valid"]:
            print("✅ 恢復的配置驗證通過")
        else:
            print("⚠️  恢復的配置驗證有問題:")
            for error in validation_result["errors"]:
                print(f"   錯誤: {error}")
        
    except Exception as e:
        print(f"❌ 配置恢復失敗: {e}")
        sys.exit(1)


def list_backups_command(args) -> None:
    """列出備份命令"""
    print("📋 列出配置備份...")
    
    try:
        config_manager = ConfigManager()
        backups = config_manager.list_backups()
        
        if not backups:
            print("📭 沒有找到配置備份")
            return
        
        print(f"📦 找到 {len(backups)} 個配置備份:")
        print()
        
        for i, backup in enumerate(backups, 1):
            print(f"{i}. {backup['name']}")
            print(f"   檔案: {backup['file_path']}")
            print(f"   創建時間: {backup['created_at']}")
            print(f"   創建者: {backup['created_by']}")
            print(f"   原始環境: {backup['original_env']}")
            print(f"   檔案大小: {backup['file_size']} bytes")
            print(f"   修改時間: {backup['modified_time']}")
            print()
        
    except Exception as e:
        print(f"❌ 列出備份失敗: {e}")
        sys.exit(1)


def show_command(args) -> None:
    """顯示配置命令"""
    print("📋 顯示當前配置...")
    
    try:
        config = get_dashboard_config()
        config_dict = config.to_dict()
        
        if args.format == "json":
            print(json.dumps(config_dict, indent=2, ensure_ascii=False))
        else:
            # 格式化顯示
            print(f"環境: {config_dict['env']}")
            print()
            
            print("📊 更新間隔配置:")
            intervals = config_dict['update_intervals']
            print(f"   指標收集間隔: {intervals['metrics_collection']} 秒")
            print(f"   告警評估間隔: {intervals['alerts_evaluation']} 秒")
            print(f"   趨勢分析間隔: {intervals['trends_analysis']} 秒")
            print(f"   WebSocket心跳間隔: {intervals['websocket_heartbeat']} 秒")
            print()
            
            print("🚨 告警閾值配置:")
            thresholds = config_dict['alert_thresholds']
            print(f"   郵件待處理: 警告={thresholds['email_pending_warning']}, 嚴重={thresholds['email_pending_critical']}")
            print(f"   Dramatiq待處理: 警告={thresholds['dramatiq_pending_warning']}, 嚴重={thresholds['dramatiq_pending_critical']}")
            print(f"   CPU使用率: 警告={thresholds['cpu_warning']}%, 嚴重={thresholds['cpu_critical']}%")
            print(f"   記憶體使用率: 警告={thresholds['memory_warning']}%, 嚴重={thresholds['memory_critical']}%")
            print(f"   磁碟使用率: 警告={thresholds['disk_warning']}%, 嚴重={thresholds['disk_critical']}%")
            print()
            
            print("📅 資料保留策略:")
            retention = config_dict['retention_policies']
            print(f"   指標歷史: {retention['metrics_history']} 天")
            print(f"   告警歷史: {retention['alerts_history']} 天")
            print(f"   任務執行歷史: {retention['task_execution_history']} 天")
            print()
            
            print("🔗 WebSocket配置:")
            websocket = config_dict['websocket_config']
            print(f"   最大連接數: {websocket['max_connections']}")
            print(f"   心跳間隔: {websocket['heartbeat_interval']} 秒")
            print(f"   連接超時: {websocket['connection_timeout']} 秒")
            print()
            
            print("🗄️  資料庫配置:")
            database = config_dict['database_config']
            print(f"   資料庫路徑: {database['db_path']}")
            print(f"   連接池大小: {database['connection_pool_size']}")
            print(f"   連接超時: {database['connection_timeout']} 秒")
        
    except Exception as e:
        print(f"❌ 顯示配置失敗: {e}")
        sys.exit(1)


def env_command(args) -> None:
    """顯示環境變數覆蓋命令"""
    print("🌍 顯示環境變數覆蓋...")
    
    try:
        config_manager = ConfigManager()
        env_overrides = config_manager.get_environment_overrides()
        
        if not env_overrides:
            print("📭 沒有設定環境變數覆蓋")
            return
        
        print(f"🔧 找到 {len(env_overrides)} 個環境變數覆蓋:")
        print()
        
        for env_var, value in env_overrides.items():
            print(f"   {env_var} = {value}")
        
    except Exception as e:
        print(f"❌ 顯示環境變數失敗: {e}")
        sys.exit(1)


def export_command(args) -> None:
    """匯出配置命令"""
    print(f"📤 匯出配置到: {args.output_file}")
    
    try:
        config_manager = ConfigManager()
        output_file = Path(args.output_file)
        config_manager.export_config(output_file)
        print(f"✅ 配置已匯出到: {output_file}")
        
    except Exception as e:
        print(f"❌ 配置匯出失敗: {e}")
        sys.exit(1)


def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description="統一監控儀表板配置管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python config_cli.py validate --verbose          # 驗證配置並顯示詳細資訊
  python config_cli.py backup --name my_backup     # 備份配置
  python config_cli.py restore backup_file.json    # 恢復配置
  python config_cli.py list-backups                # 列出所有備份
  python config_cli.py show --format json          # 以JSON格式顯示配置
  python config_cli.py env                         # 顯示環境變數覆蓋
  python config_cli.py export config.json          # 匯出配置到檔案
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 驗證命令
    validate_parser = subparsers.add_parser("validate", help="驗證配置")
    validate_parser.add_argument("--verbose", "-v", action="store_true", help="顯示詳細資訊")
    
    # 備份命令
    backup_parser = subparsers.add_parser("backup", help="備份配置")
    backup_parser.add_argument("--name", "-n", help="備份名稱")
    
    # 恢復命令
    restore_parser = subparsers.add_parser("restore", help="恢復配置")
    restore_parser.add_argument("backup_file", help="備份檔案路徑")
    
    # 列出備份命令
    subparsers.add_parser("list-backups", help="列出所有備份")
    
    # 顯示配置命令
    show_parser = subparsers.add_parser("show", help="顯示當前配置")
    show_parser.add_argument("--format", choices=["text", "json"], default="text", help="輸出格式")
    
    # 環境變數命令
    subparsers.add_parser("env", help="顯示環境變數覆蓋")
    
    # 匯出命令
    export_parser = subparsers.add_parser("export", help="匯出配置到檔案")
    export_parser.add_argument("output_file", help="輸出檔案路徑")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 執行對應命令
    command_map = {
        "validate": validate_command,
        "backup": backup_command,
        "restore": restore_command,
        "list-backups": list_backups_command,
        "show": show_command,
        "env": env_command,
        "export": export_command
    }
    
    command_func = command_map.get(args.command)
    if command_func:
        command_func(args)
    else:
        print(f"❌ 未知命令: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()