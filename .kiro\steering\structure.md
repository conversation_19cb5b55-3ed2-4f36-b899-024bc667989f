---
inclusion: always
---

# Project Structure & Development Guidelines

## Architecture Rules

**MUST follow hexagonal architecture patterns:**
- Domain layer (`src/domain/`) contains NO external dependencies
- Infrastructure layer (`src/infrastructure/`) adapts external systems to domain interfaces
- Application layer (`src/application/`) orchestrates use cases
- Presentation layer (`src/presentation/`) handles external communication
- Dependencies MUST point inward toward domain

**MUST use dependency injection:**
- Constructor-based injection throughout
- Interfaces defined in `src/application/interfaces/`
- Implementations in `src/infrastructure/adapters/`

## Code Organization Patterns

### New File Placement Rules
- **Domain entities**: `src/domain/entities/` - Core business objects
- **Value objects**: `src/domain/value_objects/` - Immutable data structures
- **Use cases**: `src/application/use_cases/` - Business workflows
- **API endpoints**: `src/presentation/api/` - REST controllers
- **Database models**: `src/infrastructure/database/` - Persistence layer
- **External adapters**: `src/infrastructure/adapters/` - Third-party integrations
- **Vendor parsers**: `src/infrastructure/parsers/` - Vendor-specific logic
- **Application services**: `src/services/` - Cross-cutting concerns
- **DTOs**: `src/data_models/` - Data transfer objects
- **Utilities**: `src/utils/` - Shared helper functions

### Frontend Module Structure (NEW - 2025-01-08)
- **Frontend modules**: `frontend/` - Modular Flask frontend architecture
  - **Main application**: `frontend/app.py` - Flask factory pattern with blueprint registration
  - **Configuration**: `frontend/config.py` - Multi-environment configuration management
  - **Shared utilities**: `frontend/shared/` - Common templates, CSS, JS, error handling
  - **Module routes**: `frontend/{module}/routes/` - Blueprint-based route definitions
  - **Module templates**: `frontend/{module}/templates/` - Module-specific HTML templates
  - **Module assets**: `frontend/{module}/static/` - Module-specific CSS, JS, images
  - **Module components**: `frontend/{module}/components/` - Reusable HTML components

### Monitoring Dashboard Structure
- **Dashboard monitoring**: `src/dashboard_monitoring/` - Unified monitoring system
  - **Core services**: `src/dashboard_monitoring/core/` - Monitoring coordinator, alert service
  - **Data collectors**: `src/dashboard_monitoring/collectors/` - Email, Dramatiq, system collectors
  - **Data models**: `src/dashboard_monitoring/models/` - Metrics and alert models
  - **Repositories**: `src/dashboard_monitoring/repositories/` - Data access layer
  - **API endpoints**: `src/dashboard_monitoring/api/` - Monitoring REST/WebSocket APIs
  - **Configuration**: `src/dashboard_monitoring/config/` - Dashboard configuration
  - **Templates**: `src/dashboard_monitoring/templates/` - HTML templates
  - **Static assets**: `src/dashboard_monitoring/static/` - CSS, JS, images

### Testing Organization
- **Unit tests**: `tests/unit/` - Single component isolation
- **Integration tests**: `tests/integration/` - Multi-component workflows
- **E2E tests**: `tests/e2e/` - Full system scenarios
- **Test fixtures**: `tests/fixtures/` - Shared test data
- **MUST achieve 90%+ coverage for domain logic**

## Naming Conventions

### Files & Directories
- **MUST use snake_case** for all Python files and directories
- **MUST use descriptive names** that clearly indicate purpose
- **MUST use consistent suffixes**: `_parser.py`, `_service.py`, `_test.py`, `_api.py`

### Code Elements
- **Classes**: PascalCase (`EmailProcessor`, `VendorParser`)
- **Functions/Variables**: snake_case (`process_email`, `vendor_type`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`)
- **Private members**: Leading underscore (`_internal_method`, `_config`)
- **Interfaces**: Prefix with 'I' (`IEmailRepository`, `IParserFactory`)

## Vendor-Specific Development

### Parser Implementation
- **MUST inherit from** `BaseParser` in `src/infrastructure/parsers/base_parser.py`
- **MUST implement** all abstract methods
- **MUST handle** vendor-specific file formats and keywords
- **MUST include** comprehensive error handling and logging

### Supported Vendors & Keywords
- **ETD**: `anf` keyword
- **GTK**: `ft hold`, `ft lot` keywords
- **JCET**: `jcet` keyword
- **LINGSEN**: `lingsen` keyword
- **XAHT**: `tianshui`, `西安` keywords

### Adding New Vendors
1. Create parser in `src/infrastructure/parsers/{vendor}_parser.py`
2. Register in parser factory
3. Add keyword mapping in email classifier
4. Create corresponding unit and integration tests
5. Update documentation

## Import Organization

**MUST follow this order:**
```python
# 1. Standard library imports
import os
from typing import List, Optional, Dict

# 2. Third-party library imports
import pandas as pd
from fastapi import FastAPI
from sqlalchemy import Column

# 3. Local application imports (grouped by layer)
from src.domain.entities import Email
from src.application.interfaces import IEmailRepository
from src.infrastructure.parsers import BaseParser
```

## File Creation Guidelines

### New Classes
- **One class per file** (except small related classes)
- **Include type hints** for all method parameters and return values
- **Add docstrings** for all public methods
- **Implement proper error handling** with domain-specific exceptions

### New Services
- **Define interface first** in `src/application/interfaces/`
- **Implement in appropriate infrastructure layer**
- **Include comprehensive logging** using structured logging
- **Add dependency injection support**

### New Tests
- **Follow AAA pattern** (Arrange, Act, Assert)
- **Use descriptive test names** that explain the scenario
- **Mock external dependencies** in unit tests
- **Include both happy path and error scenarios**

## Special Considerations

### Windows Compatibility
- **MUST handle** Windows path separators correctly
- **MUST set** UTF-8 encoding: `PYTHONIOENCODING=utf-8`
- **MUST use** `pathlib.Path` for cross-platform paths

### Data Processing
- **MUST validate** MO and LOT identifiers in all vendor data
- **MUST handle** CSV, Excel, and compressed file formats
- **MUST implement** retry mechanisms for transient failures
- **MUST log** all parsing attempts for audit trails

### Service Architecture
- **Flask service** (Port 5000): Email inbox management
- **FastAPI service** (Port 8010): FT-EQC processing with `/ui` and `/docs`
- **MUST maintain** clear service boundaries and responsibilities