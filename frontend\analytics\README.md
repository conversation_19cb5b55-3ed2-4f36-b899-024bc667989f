# Analytics Module - 分析統計功能模組

## 概述

分析統計功能模組負責處理半導體測試數據的統計分析、報表生成和廠商分析功能，提供數據洞察和業務智能。

## 功能特性

### 核心功能
- **統計儀表板** - 顯示關鍵業務指標和趨勢圖表
- **報表生成** - 自動生成 Excel 格式的統計報表
- **廠商分析** - 分析不同廠商的數據品質和效能
- **CSV 處理** - 批次處理和轉換 CSV 數據檔案

### 關鍵指標
- **MO (Manufacturing Order)** - 製造訂單統計
- **LOT** - 測試批次分析
- **Yield Rate** - 良率統計和趨勢
- **Data Quality** - 數據品質指標
- **Processing Time** - 處理時間分析

## 目錄結構

```
analytics/
├── templates/               # HTML 模板
│   ├── dashboard.html       # 統計儀表板
│   ├── reports.html         # 報表頁面
│   ├── vendor_analysis.html # 廠商分析
│   └── csv_processor.html   # CSV 處理頁面
├── static/                  # 靜態資源
│   ├── css/
│   │   └── analytics.css    # 統計專用樣式
│   ├── js/
│   │   ├── charts.js        # 圖表邏輯
│   │   ├── reports.js       # 報表邏輯
│   │   └── analytics-api.js # 統計 API
│   └── lib/                 # 第三方庫
│       └── chart.min.js     # Chart.js 圖表庫
├── components/              # 可重用組件
│   ├── chart-widget.html    # 圖表組件
│   ├── data-table.html      # 數據表格
│   └── export-button.html   # 導出按鈕
├── routes/                  # 路由處理
│   └── analytics_routes.py  # 統計路由
└── README.md                # 本檔案
```

## API 端點

### 儀表板數據
- `GET /analytics/dashboard` - 統計儀表板頁面
- `GET /api/analytics/metrics` - 獲取關鍵指標
- `GET /api/analytics/charts/<type>` - 獲取圖表數據
- `GET /api/analytics/trends` - 獲取趨勢數據

### 報表管理
- `GET /analytics/reports` - 報表管理頁面
- `GET /api/analytics/reports` - 獲取報表列表
- `POST /api/analytics/reports/generate` - 生成新報表
- `GET /api/analytics/reports/<id>/download` - 下載報表

### 廠商分析
- `GET /analytics/vendors` - 廠商分析頁面
- `GET /api/analytics/vendors/stats` - 獲取廠商統計
- `GET /api/analytics/vendors/<vendor>/performance` - 廠商效能分析

### CSV 處理
- `GET /analytics/csv-processor` - CSV 處理頁面
- `POST /api/analytics/csv/upload` - 上傳 CSV 檔案
- `POST /api/analytics/csv/process` - 處理 CSV 數據
- `GET /api/analytics/csv/results/<job_id>` - 獲取處理結果

## 資料模型

### AnalyticsMetrics
- `metric_name`: 指標名稱
- `metric_value`: 指標值
- `metric_type`: 指標類型 (count, percentage, duration)
- `timestamp`: 時間戳記
- `vendor_type`: 廠商類型 (可選)

### Report
- `id`: 報表唯一識別碼
- `title`: 報表標題
- `type`: 報表類型 (daily, weekly, monthly)
- `status`: 生成狀態 (pending, completed, failed)
- `file_path`: 檔案路徑
- `created_at`: 建立時間
- `parameters`: 報表參數

### VendorStats
- `vendor_type`: 廠商類型
- `total_emails`: 總郵件數
- `processed_count`: 已處理數量
- `success_rate`: 成功率
- `avg_processing_time`: 平均處理時間
- `last_updated`: 最後更新時間

## 圖表類型

### 支援的圖表
- **Line Chart** - 趨勢線圖 (良率趨勢、處理時間趨勢)
- **Bar Chart** - 柱狀圖 (廠商比較、月度統計)
- **Pie Chart** - 圓餅圖 (廠商分佈、狀態分佈)
- **Gauge Chart** - 儀表圖 (良率指標、效能指標)
- **Heatmap** - 熱力圖 (時間分佈、品質分佈)

## 開發注意事項

- 使用 Chart.js 進行圖表渲染
- 實作數據快取機制提升效能
- 支援即時數據更新 (WebSocket)
- 確保大數據集的分頁和延遲載入
- 提供數據導出功能 (Excel, CSV, PDF)
- 實作適當的數據權限控制