"""
統一監控儀表板 - WebSocket 管理服務

實現即時監控資料推送的 WebSocket 管理系統，包括：
- WebSocket 連接管理器
- 訂閱機制
- 廣播功能  
- 連接異常處理

符合需求 4：即時更新和通知機制
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, field

from fastapi import WebSocket, WebSocketDisconnect, status
from fastapi.routing import APIRouter

try:
    from .dashboard_dependencies import (
        Config,
        OptionalMonitoringCoordinator,
        get_dashboard_config
    )
except ImportError:
    # Fallback for when dependencies are not available
    def get_dashboard_config():
        from dataclasses import dataclass, field
        @dataclass
        class MockWebSocketConfig:
            max_connections: int = 100
            message_queue_size: int = 1000
            heartbeat_interval: int = 30
            connection_timeout: int = 300
        
        @dataclass
        class MockConfig:
            websocket_config: MockWebSocketConfig = field(default_factory=MockWebSocketConfig)
        
        return MockConfig()

try:
    from ..models.dashboard_models import DashboardState
except ImportError:
    # Fallback for when models are not available
    class DashboardState:
        def to_dict(self):
            return {"status": "mock"}

try:
    from ..utils.dashboard_helpers import log_performance, handle_dashboard_error
except ImportError:
    # Fallback decorators
    def log_performance(func):
        return func
    
    def handle_dashboard_error(func):
        return func

logger = logging.getLogger(__name__)

# WebSocket 路由器
websocket_router = APIRouter(tags=["WebSocket 監控"])


class DashboardSubscriptionType(Enum):
    """儀表板訂閱類型"""
    ALL = "all"                    # 訂閱所有更新
    METRICS = "metrics"            # 只訂閱指標更新
    METRICS_UPDATE = "metrics_update"  # 指標更新（前端期望的類型）
    ALERTS = "alerts"              # 只訂閱告警更新
    ALERT = "alert"                # 告警（前端期望的類型）
    EMAIL_QUEUE = "email_queue"    # 只訂閱郵件佇列更新
    EMAIL_METRICS = "email_metrics"  # 郵件指標（前端期望的類型）
    DRAMATIQ_TASKS = "dramatiq_tasks"  # 只訂閱 Dramatiq 任務更新
    DRAMATIQ_METRICS = "dramatiq_metrics"  # Dramatiq 指標（前端期望的類型）
    SYSTEM_HEALTH = "system_health" # 只訂閱系統健康狀態更新
    SYSTEM_STATUS = "system_status"  # 系統狀態（前端期望的類型）
    SYSTEM_METRICS = "system_metrics"  # 系統指標（前端期望的類型）
    BUSINESS_METRICS = "business_metrics"  # 只訂閱業務指標更新
    FILE_METRICS = "file_metrics"  # 檔案指標（前端期望的類型）
    NETWORK_METRICS = "network_metrics"  # 網路指標


class DashboardMessageType(Enum):
    """WebSocket 訊息類型"""
    # 客戶端發送的訊息
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    PING = "ping"
    HEARTBEAT = "heartbeat"  # 添加心跳類型
    
    # 伺服器發送的訊息
    METRICS_UPDATE = "metrics_update"
    ALERT = "alert"
    SYSTEM_STATUS = "system_status"
    PONG = "pong"
    ERROR = "error"
    CONNECTION_INFO = "connection_info"


@dataclass
class DashboardWebSocketMessage:
    """WebSocket 訊息格式"""
    type: DashboardMessageType
    payload: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    client_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "type": self.type.value,
            "payload": self.payload,
            "timestamp": self.timestamp.isoformat(),
            "client_id": self.client_id
        }
    
    def to_json(self) -> str:
        """轉換為 JSON 字串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)


@dataclass
class DashboardWebSocketClient:
    """WebSocket 客戶端資訊"""
    client_id: str
    websocket: WebSocket
    subscriptions: Set[DashboardSubscriptionType] = field(default_factory=set)
    connected_at: datetime = field(default_factory=datetime.now)
    last_ping: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    user_agent: Optional[str] = None
    remote_address: Optional[str] = None
    
    # 連接統計
    messages_sent: int = 0
    messages_received: int = 0
    
    def is_subscribed_to(self, subscription_type: DashboardSubscriptionType) -> bool:
        """檢查是否訂閱了特定類型"""
        return (DashboardSubscriptionType.ALL in self.subscriptions or 
                subscription_type in self.subscriptions)
    
    def update_activity(self) -> None:
        """更新最後活動時間"""
        self.last_activity = datetime.now()
    
    def is_alive(self, timeout_seconds: int = 300) -> bool:
        """檢查連接是否存活"""
        return (datetime.now() - self.last_activity).total_seconds() < timeout_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "client_id": self.client_id,
            "subscriptions": [sub.value for sub in self.subscriptions],
            "connected_at": self.connected_at.isoformat(),
            "last_ping": self.last_ping.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "user_agent": self.user_agent,
            "remote_address": self.remote_address,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "is_alive": self.is_alive()
        }


class DashboardWebSocketManager:
    """統一監控儀表板 WebSocket 管理器
    
    負責管理所有 WebSocket 連接，實現：
    - 連接管理
    - 訂閱機制
    - 廣播功能
    - 心跳檢測
    - 異常處理
    """
    
    def __init__(self, config: Optional[Any] = None):
        self.config = config or get_dashboard_config()
        self.clients: Dict[str, DashboardWebSocketClient] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue(
            maxsize=self.config.websocket_config.message_queue_size
        )
        
        # 統計資訊
        self.total_connections = 0
        self.total_messages_sent = 0
        self.total_messages_received = 0
        self.start_time = datetime.now()
        
        # 背景任務
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._broadcast_task: Optional[asyncio.Task] = None
        
        logger.info("WebSocket 管理器已初始化")
    
    async def start_background_tasks(self) -> None:
        """啟動背景任務"""
        if not self._heartbeat_task or self._heartbeat_task.done():
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            logger.info("心跳檢測任務已啟動")
        
        if not self._cleanup_task or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("清理任務已啟動")
        
        if not self._broadcast_task or self._broadcast_task.done():
            self._broadcast_task = asyncio.create_task(self._broadcast_loop())
            logger.info("廣播任務已啟動")
    
    async def stop_background_tasks(self) -> None:
        """停止背景任務"""
        tasks = [self._heartbeat_task, self._cleanup_task, self._broadcast_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("所有背景任務已停止")
    
    async def connect_client(self, websocket: WebSocket, client_id: str) -> DashboardWebSocketClient:
        """連接新客戶端"""
        # 檢查連接數限制
        if len(self.clients) >= self.config.websocket_config.max_connections:
            logger.warning(f"達到最大連接數限制: {self.config.websocket_config.max_connections}")
            await websocket.close(code=status.WS_1013_TRY_AGAIN_LATER)
            raise Exception("達到最大連接數限制")
        
        # 接受 WebSocket 連接
        await websocket.accept()
        
        # 創建客戶端物件
        client = DashboardWebSocketClient(
            client_id=client_id,
            websocket=websocket,
            user_agent=websocket.headers.get("user-agent"),
            remote_address=websocket.client.host if websocket.client else None
        )
        
        # 註冊客戶端
        self.clients[client_id] = client
        self.total_connections += 1
        
        # 發送連接確認訊息
        await self._send_to_client(client, DashboardWebSocketMessage(
            type=DashboardMessageType.CONNECTION_INFO,
            payload={
                "client_id": client_id,
                "connected_at": client.connected_at.isoformat(),
                "server_time": datetime.now().isoformat(),
                "max_connections": self.config.websocket_config.max_connections,
                "heartbeat_interval": self.config.websocket_config.heartbeat_interval
            },
            client_id=client_id
        ))
        
        logger.info(f"客戶端已連接: {client_id} (總連接數: {len(self.clients)})")
        return client
    
    async def disconnect_client(self, client_id: str, reason: str = "正常斷線") -> None:
        """斷開客戶端連接"""
        if client_id in self.clients:
            client = self.clients[client_id]
            
            try:
                # 嘗試優雅關閉 WebSocket
                if not client.websocket.client_state.DISCONNECTED:
                    await client.websocket.close()
            except Exception as e:
                logger.debug(f"關閉 WebSocket 時發生錯誤: {e}")
            
            # 移除客戶端
            del self.clients[client_id]
            
            logger.info(f"客戶端已斷線: {client_id}, 原因: {reason} (剩餘連接數: {len(self.clients)})")
    
    async def handle_client_message(self, client: DashboardWebSocketClient, message_data: str) -> None:
        """處理客戶端訊息"""
        try:
            # 解析訊息
            message_dict = json.loads(message_data)
            message_type = DashboardMessageType(message_dict.get("type"))
            payload = message_dict.get("payload", {})
            
            client.messages_received += 1
            client.update_activity()
            self.total_messages_received += 1
            
            # 處理不同類型的訊息
            if message_type == DashboardMessageType.SUBSCRIBE:
                await self._handle_subscribe(client, payload)
            elif message_type == DashboardMessageType.UNSUBSCRIBE:
                await self._handle_unsubscribe(client, payload)
            elif message_type == DashboardMessageType.PING:
                await self._handle_ping(client, payload)
            elif message_type == DashboardMessageType.HEARTBEAT:
                await self._handle_heartbeat(client, payload)
            else:
                logger.warning(f"未知的訊息類型: {message_type}")
                await self._send_error(client, f"未知的訊息類型: {message_type}")
        
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析錯誤: {e}")
            await self._send_error(client, "訊息格式錯誤")
        except ValueError as e:
            logger.error(f"訊息類型錯誤: {e}")
            await self._send_error(client, "無效的訊息類型")
        except Exception as e:
            logger.error(f"處理客戶端訊息時發生錯誤: {e}")
            await self._send_error(client, "處理訊息時發生內部錯誤")
    
    async def _handle_subscribe(self, client: DashboardWebSocketClient, payload: Dict[str, Any]) -> None:
        """處理訂閱請求"""
        subscription_types = payload.get("types", [])
        
        if not subscription_types:
            await self._send_error(client, "訂閱類型不能為空")
            return
        
        # 驗證並添加訂閱
        valid_subscriptions = []
        invalid_subscriptions = []
        
        for sub_type in subscription_types:
            try:
                subscription = DashboardSubscriptionType(sub_type)
                client.subscriptions.add(subscription)
                valid_subscriptions.append(subscription.value)
            except ValueError:
                invalid_subscriptions.append(sub_type)
                logger.warning(f"無效的訂閱類型: {sub_type}")
        
        # 發送訂閱確認
        await self._send_to_client(client, DashboardWebSocketMessage(
            type=DashboardMessageType.CONNECTION_INFO,
            payload={
                "action": "subscribed",
                "subscriptions": valid_subscriptions,
                "invalid_subscriptions": invalid_subscriptions,
                "total_subscriptions": len(client.subscriptions)
            },
            client_id=client.client_id
        ))
        
        if valid_subscriptions:
            logger.info(f"客戶端 {client.client_id} 已訂閱: {valid_subscriptions}")
        if invalid_subscriptions:
            logger.warning(f"客戶端 {client.client_id} 嘗試訂閱無效類型: {invalid_subscriptions}")
    
    async def _handle_unsubscribe(self, client: DashboardWebSocketClient, payload: Dict[str, Any]) -> None:
        """處理取消訂閱請求"""
        subscription_types = payload.get("types", [])
        
        if not subscription_types:
            # 取消所有訂閱
            client.subscriptions.clear()
            unsubscribed = ["all"]
        else:
            # 取消特定訂閱
            unsubscribed = []
            for sub_type in subscription_types:
                try:
                    subscription = DashboardSubscriptionType(sub_type)
                    if subscription in client.subscriptions:
                        client.subscriptions.remove(subscription)
                        unsubscribed.append(subscription.value)
                except ValueError:
                    logger.warning(f"無效的訂閱類型: {sub_type}")
        
        # 發送取消訂閱確認
        await self._send_to_client(client, DashboardWebSocketMessage(
            type=DashboardMessageType.CONNECTION_INFO,
            payload={
                "action": "unsubscribed",
                "unsubscribed": unsubscribed,
                "remaining_subscriptions": [sub.value for sub in client.subscriptions]
            },
            client_id=client.client_id
        ))
        
        logger.info(f"客戶端 {client.client_id} 已取消訂閱: {unsubscribed}")
    
    async def _handle_ping(self, client: DashboardWebSocketClient, payload: Dict[str, Any]) -> None:
        """處理心跳請求"""
        client.last_ping = datetime.now()
        
        # 發送 PONG 回應
        await self._send_to_client(client, DashboardWebSocketMessage(
            type=DashboardMessageType.PONG,
            payload={
                "server_time": datetime.now().isoformat(),
                "client_ping_time": payload.get("client_time"),
                "connection_alive": True
            },
            client_id=client.client_id
        ))
    
    async def _handle_heartbeat(self, client: DashboardWebSocketClient, payload: Dict[str, Any]) -> None:
        """處理心跳訊息"""
        client.last_ping = datetime.now()
        client.update_activity()
        
        # 心跳訊息通常不需要回應，只更新客戶端活動狀態
        logger.debug(f"收到客戶端 {client.client_id} 的心跳訊息")
    
    async def broadcast_metrics_update(self, metrics_data: Dict[str, Any]) -> None:
        """廣播指標更新"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.METRICS_UPDATE,
            payload=metrics_data
        )
        
        await self._queue_broadcast(message, DashboardSubscriptionType.METRICS)
    
    async def broadcast_alert(self, alert_data: Dict[str, Any]) -> None:
        """廣播告警"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.ALERT,
            payload=alert_data
        )
        
        await self._queue_broadcast(message, DashboardSubscriptionType.ALERTS)
    
    async def broadcast_system_status(self, status_data: Dict[str, Any]) -> None:
        """廣播系統狀態"""
        message = DashboardWebSocketMessage(
            type=DashboardMessageType.SYSTEM_STATUS,
            payload=status_data
        )
        
        await self._queue_broadcast(message, DashboardSubscriptionType.SYSTEM_HEALTH)
    
    async def _queue_broadcast(self, message: DashboardWebSocketMessage, subscription_type: DashboardSubscriptionType) -> None:
        """將廣播訊息加入佇列"""
        try:
            await self.message_queue.put((message, subscription_type))
        except asyncio.QueueFull:
            logger.warning("訊息佇列已滿，丟棄訊息")
    
    async def _broadcast_loop(self) -> None:
        """廣播循環任務"""
        while True:
            try:
                # 從佇列取得訊息
                message, subscription_type = await self.message_queue.get()
                
                # 找到訂閱該類型的客戶端
                target_clients = [
                    client for client in self.clients.values()
                    if client.is_subscribed_to(subscription_type)
                ]
                
                # 並行發送給所有目標客戶端
                if target_clients:
                    await asyncio.gather(
                        *[self._send_to_client(client, message) for client in target_clients],
                        return_exceptions=True
                    )
                
                self.message_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"廣播循環發生錯誤: {e}")
                await asyncio.sleep(1)
    
    async def _send_to_client(self, client: DashboardWebSocketClient, message: DashboardWebSocketMessage) -> None:
        """發送訊息給特定客戶端"""
        try:
            await client.websocket.send_text(message.to_json())
            client.messages_sent += 1
            client.update_activity()
            self.total_messages_sent += 1
            
        except WebSocketDisconnect:
            logger.info(f"客戶端 {client.client_id} 已斷線")
            await self.disconnect_client(client.client_id, "WebSocket 斷線")
        except Exception as e:
            logger.error(f"發送訊息給客戶端 {client.client_id} 失敗: {e}")
            await self.disconnect_client(client.client_id, f"發送錯誤: {e}")
    
    async def _send_error(self, client: DashboardWebSocketClient, error_message: str) -> None:
        """發送錯誤訊息給客戶端"""
        error_msg = DashboardWebSocketMessage(
            type=DashboardMessageType.ERROR,
            payload={"error": error_message},
            client_id=client.client_id
        )
        await self._send_to_client(client, error_msg)
    
    async def _heartbeat_loop(self) -> None:
        """心跳檢測循環"""
        while True:
            try:
                await asyncio.sleep(self.config.websocket_config.heartbeat_interval)
                
                # 檢查所有客戶端的心跳
                current_time = datetime.now()
                timeout_clients = []
                
                for client_id, client in self.clients.items():
                    time_since_activity = (current_time - client.last_activity).total_seconds()
                    
                    if time_since_activity > self.config.websocket_config.connection_timeout:
                        timeout_clients.append(client_id)
                
                # 斷開超時的客戶端
                for client_id in timeout_clients:
                    await self.disconnect_client(client_id, "心跳超時")
                
                if timeout_clients:
                    logger.info(f"清理了 {len(timeout_clients)} 個超時連接")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳檢測發生錯誤: {e}")
    
    async def _cleanup_loop(self) -> None:
        """清理循環任務"""
        while True:
            try:
                await asyncio.sleep(300)  # 每 5 分鐘執行一次清理
                
                # 清理斷線的客戶端
                disconnected_clients = []
                for client_id, client in self.clients.items():
                    if not client.is_alive():
                        disconnected_clients.append(client_id)
                
                for client_id in disconnected_clients:
                    await self.disconnect_client(client_id, "連接已失效")
                
                if disconnected_clients:
                    logger.info(f"清理了 {len(disconnected_clients)} 個失效連接")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理任務發生錯誤: {e}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """獲取連接統計資訊"""
        current_time = datetime.now()
        uptime = (current_time - self.start_time).total_seconds()
        
        return {
            "current_connections": len(self.clients),
            "max_connections": self.config.websocket_config.max_connections,
            "total_connections": self.total_connections,
            "total_messages_sent": self.total_messages_sent,
            "total_messages_received": self.total_messages_received,
            "uptime_seconds": uptime,
            "message_queue_size": self.message_queue.qsize(),
            "clients": [client.to_dict() for client in self.clients.values()]
        }


# 全域 WebSocket 管理器實例
_websocket_manager: Optional[DashboardWebSocketManager] = None


def get_websocket_manager() -> DashboardWebSocketManager:
    """獲取 WebSocket 管理器單例"""
    global _websocket_manager
    
    if _websocket_manager is None:
        config = get_dashboard_config()
        _websocket_manager = DashboardWebSocketManager(config)
    
    return _websocket_manager


@websocket_router.websocket("/ws")
async def dashboard_websocket_endpoint(websocket: WebSocket):
    """
    統一監控儀表板 WebSocket 端點
    
    支援的訊息類型：
    - subscribe: 訂閱特定類型的更新
    - unsubscribe: 取消訂閱
    - ping: 心跳檢測
    
    伺服器推送的訊息類型：
    - metrics_update: 指標更新
    - alert: 告警通知
    - system_status: 系統狀態更新
    - pong: 心跳回應
    - error: 錯誤訊息
    - connection_info: 連接資訊
    """
    manager = get_websocket_manager()
    
    # 生成客戶端 ID
    client_id = f"dashboard_{uuid.uuid4().hex[:8]}_{int(datetime.now().timestamp())}"
    
    # 確保背景任務正在運行
    await manager.start_background_tasks()
    
    try:
        # 連接客戶端
        client = await manager.connect_client(websocket, client_id)
        
        # 處理客戶端訊息
        while True:
            try:
                # 接收客戶端訊息
                message = await websocket.receive_text()
                await manager.handle_client_message(client, message)
                
            except WebSocketDisconnect:
                logger.info(f"客戶端 {client_id} 主動斷線")
                break
            except Exception as e:
                logger.error(f"處理客戶端 {client_id} 訊息時發生錯誤: {e}")
                break
    
    except Exception as e:
        logger.error(f"WebSocket 連接 {client_id} 發生錯誤: {e}")
    
    finally:
        # 確保客戶端被正確斷線
        await manager.disconnect_client(client_id, "連接結束")


# 用於監控協調器的 WebSocket 管理器介面
async def broadcast_dashboard_update(data: Dict[str, Any], update_type: str = "metrics") -> None:
    """廣播儀表板更新（供監控協調器使用）"""
    manager = get_websocket_manager()
    
    if update_type == "metrics":
        await manager.broadcast_metrics_update(data)
    elif update_type == "alert":
        await manager.broadcast_alert(data)
    elif update_type == "system_status":
        await manager.broadcast_system_status(data)
    else:
        logger.warning(f"未知的更新類型: {update_type}")


async def get_websocket_stats() -> Dict[str, Any]:
    """獲取 WebSocket 統計資訊（供 API 使用）"""
    manager = get_websocket_manager()
    return manager.get_connection_stats()