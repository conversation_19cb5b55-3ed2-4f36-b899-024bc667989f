"""
數據源檢測器 - 自動發現和連接真實的系統數據源

此模組負責檢測系統中可用的真實數據源，包括：
- 資料庫文件
- 服務進程
- 日誌文件
- 配置文件
- 網路服務
"""

import os
import glob
import sqlite3
import psutil
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DataSourceDetector:
    """數據源檢測器"""
    
    def __init__(self):
        self.detected_sources = {}
        self.last_detection = None
        
    async def detect_all_sources(self) -> Dict[str, Any]:
        """檢測所有可用的數據源"""
        try:
            logger.info("開始檢測系統數據源...")
            
            sources = {
                'databases': await self._detect_databases(),
                'services': await self._detect_services(),
                'log_files': await self._detect_log_files(),
                'vendor_files': await self._detect_vendor_files(),
                'config_files': await self._detect_config_files(),
                'network_services': await self._detect_network_services(),
                'detection_time': datetime.now().isoformat()
            }
            
            self.detected_sources = sources
            self.last_detection = datetime.now()
            
            logger.info(f"數據源檢測完成，發現 {len(sources)} 類數據源")
            return sources
            
        except Exception as e:
            logger.error(f"數據源檢測失敗: {e}")
            return {}
    
    async def _detect_databases(self) -> Dict[str, Any]:
        """檢測資料庫文件"""
        databases = {}
        
        # 常見的資料庫文件模式
        db_patterns = [
            "*.db",
            "*.sqlite",
            "*.sqlite3",
            "data/*.db",
            "data/*.sqlite",
            "**/*.db"
        ]
        
        for pattern in db_patterns:
            try:
                for db_path in glob.glob(pattern, recursive=True):
                    if os.path.isfile(db_path):
                        db_info = await self._analyze_database(db_path)
                        if db_info:
                            databases[db_path] = db_info
            except Exception as e:
                logger.debug(f"檢測資料庫模式 {pattern} 失敗: {e}")
        
        return databases
    
    async def _analyze_database(self, db_path: str) -> Optional[Dict[str, Any]]:
        """分析資料庫文件"""
        try:
            if not os.path.exists(db_path):
                return None
            
            # 獲取文件基本信息
            stat = os.stat(db_path)
            file_size = stat.st_size
            modified_time = datetime.fromtimestamp(stat.st_mtime)
            
            # 嘗試連接並獲取表信息
            tables = []
            record_counts = {}
            
            try:
                conn = sqlite3.connect(db_path, timeout=2.0)
                cursor = conn.cursor()
                
                # 獲取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                table_names = [row[0] for row in cursor.fetchall()]
                
                # 獲取每個表的記錄數
                for table_name in table_names:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        count = cursor.fetchone()[0]
                        record_counts[table_name] = count
                        tables.append({
                            'name': table_name,
                            'record_count': count
                        })
                    except Exception:
                        tables.append({
                            'name': table_name,
                            'record_count': 0
                        })
                
                conn.close()
                
            except Exception as e:
                logger.debug(f"分析資料庫 {db_path} 內容失敗: {e}")
            
            return {
                'path': db_path,
                'size_bytes': file_size,
                'size_mb': round(file_size / (1024 * 1024), 2),
                'modified_time': modified_time.isoformat(),
                'tables': tables,
                'total_tables': len(tables),
                'total_records': sum(record_counts.values()),
                'is_accessible': len(tables) > 0
            }
            
        except Exception as e:
            logger.debug(f"分析資料庫 {db_path} 失敗: {e}")
            return None
    
    async def _detect_services(self) -> Dict[str, Any]:
        """檢測運行中的服務"""
        services = {}
        
        # 要檢測的服務關鍵字
        service_keywords = [
            'dramatiq',
            'redis',
            'python',
            'fastapi',
            'flask',
            'uvicorn',
            'gunicorn',
            'celery'
        ]
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'cpu_percent', 'memory_info']):
                try:
                    proc_info = proc.info
                    cmdline = proc_info.get('cmdline', [])
                    
                    if not cmdline:
                        continue
                    
                    cmdline_str = ' '.join(cmdline).lower()
                    
                    # 檢查是否匹配服務關鍵字
                    for keyword in service_keywords:
                        if keyword in cmdline_str:
                            service_name = f"{keyword}_{proc_info['pid']}"
                            
                            services[service_name] = {
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'cmdline': cmdline,
                                'status': proc_info['status'],
                                'keyword': keyword,
                                'cpu_percent': proc_info.get('cpu_percent', 0),
                                'memory_mb': proc_info.get('memory_info', {}).get('rss', 0) / (1024 * 1024) if proc_info.get('memory_info') else 0
                            }
                            break
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        
        except Exception as e:
            logger.error(f"檢測服務失敗: {e}")
        
        return services
    
    async def _detect_log_files(self) -> Dict[str, Any]:
        """檢測日誌文件"""
        log_files = {}
        
        # 常見的日誌文件模式
        log_patterns = [
            "*.log",
            "logs/*.log",
            "log/*.log",
            "**/*.log"
        ]
        
        for pattern in log_patterns:
            try:
                for log_path in glob.glob(pattern, recursive=True):
                    if os.path.isfile(log_path):
                        log_info = await self._analyze_log_file(log_path)
                        if log_info:
                            log_files[log_path] = log_info
            except Exception as e:
                logger.debug(f"檢測日誌模式 {pattern} 失敗: {e}")
        
        return log_files
    
    async def _analyze_log_file(self, log_path: str) -> Optional[Dict[str, Any]]:
        """分析日誌文件"""
        try:
            stat = os.stat(log_path)
            file_size = stat.st_size
            modified_time = datetime.fromtimestamp(stat.st_mtime)
            
            # 讀取最後幾行來分析日誌內容
            recent_lines = []
            error_count = 0
            warning_count = 0
            
            try:
                with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    recent_lines = lines[-10:] if len(lines) > 10 else lines
                    
                    # 簡單的錯誤和警告計數
                    for line in lines[-100:]:  # 檢查最後100行
                        line_lower = line.lower()
                        if 'error' in line_lower or 'exception' in line_lower:
                            error_count += 1
                        elif 'warning' in line_lower or 'warn' in line_lower:
                            warning_count += 1
            
            except Exception as e:
                logger.debug(f"讀取日誌文件 {log_path} 內容失敗: {e}")
            
            return {
                'path': log_path,
                'size_bytes': file_size,
                'size_mb': round(file_size / (1024 * 1024), 2),
                'modified_time': modified_time.isoformat(),
                'recent_lines_count': len(recent_lines),
                'error_count': error_count,
                'warning_count': warning_count,
                'is_recent': (datetime.now() - modified_time).total_seconds() < 3600  # 1小時內修改
            }
            
        except Exception as e:
            logger.debug(f"分析日誌文件 {log_path} 失敗: {e}")
            return None
    
    async def _detect_vendor_files(self) -> Dict[str, Any]:
        """檢測廠商文件"""
        vendor_files = {}
        
        # 廠商文件目錄模式
        vendor_patterns = [
            "doc/*",
            "attachments/*",
            "temp/*",
            "data/*"
        ]
        
        # 支援的廠商名稱
        vendor_names = ['GTK', 'ETD', 'JCET', 'LINGSEN', 'XAHT', 'MSEC', 'NANOTECH', 'NFME', 'SUQIAN', 'TSHT', 'CHUZHOU']
        
        for pattern in vendor_patterns:
            try:
                for path in glob.glob(pattern, recursive=True):
                    if os.path.isdir(path):
                        # 檢查目錄名是否包含廠商名稱
                        dir_name = os.path.basename(path).upper()
                        for vendor in vendor_names:
                            if vendor in dir_name:
                                vendor_info = await self._analyze_vendor_directory(path, vendor)
                                if vendor_info:
                                    vendor_files[f"{vendor}_{path}"] = vendor_info
                                break
            except Exception as e:
                logger.debug(f"檢測廠商文件模式 {pattern} 失敗: {e}")
        
        return vendor_files
    
    async def _analyze_vendor_directory(self, dir_path: str, vendor_name: str) -> Optional[Dict[str, Any]]:
        """分析廠商目錄"""
        try:
            file_count = 0
            total_size = 0
            file_types = {}
            recent_files = []
            
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        stat = os.stat(file_path)
                        file_size = stat.st_size
                        modified_time = datetime.fromtimestamp(stat.st_mtime)
                        
                        file_count += 1
                        total_size += file_size
                        
                        # 統計文件類型
                        ext = os.path.splitext(file)[1].lower()
                        file_types[ext] = file_types.get(ext, 0) + 1
                        
                        # 記錄最近的文件
                        if (datetime.now() - modified_time).total_seconds() < 86400:  # 24小時內
                            recent_files.append({
                                'name': file,
                                'size': file_size,
                                'modified': modified_time.isoformat()
                            })
                    
                    except Exception:
                        continue
            
            if file_count > 0:
                return {
                    'vendor_name': vendor_name,
                    'directory': dir_path,
                    'file_count': file_count,
                    'total_size_bytes': total_size,
                    'total_size_mb': round(total_size / (1024 * 1024), 2),
                    'file_types': file_types,
                    'recent_files_count': len(recent_files),
                    'recent_files': recent_files[:5]  # 只保留前5個最近文件
                }
            
        except Exception as e:
            logger.debug(f"分析廠商目錄 {dir_path} 失敗: {e}")
        
        return None
    
    async def _detect_config_files(self) -> Dict[str, Any]:
        """檢測配置文件"""
        config_files = {}
        
        # 配置文件模式
        config_patterns = [
            "*.json",
            "*.yaml",
            "*.yml",
            "*.ini",
            "*.conf",
            "config/*",
            ".env*"
        ]
        
        for pattern in config_patterns:
            try:
                for config_path in glob.glob(pattern, recursive=True):
                    if os.path.isfile(config_path):
                        config_info = await self._analyze_config_file(config_path)
                        if config_info:
                            config_files[config_path] = config_info
            except Exception as e:
                logger.debug(f"檢測配置文件模式 {pattern} 失敗: {e}")
        
        return config_files
    
    async def _analyze_config_file(self, config_path: str) -> Optional[Dict[str, Any]]:
        """分析配置文件"""
        try:
            stat = os.stat(config_path)
            file_size = stat.st_size
            modified_time = datetime.fromtimestamp(stat.st_mtime)
            
            # 嘗試解析配置內容
            config_data = None
            file_type = None
            
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if config_path.endswith('.json'):
                    config_data = json.loads(content)
                    file_type = 'json'
                elif config_path.endswith(('.yaml', '.yml')):
                    try:
                        import yaml
                        config_data = yaml.safe_load(content)
                        file_type = 'yaml'
                    except ImportError:
                        pass
                elif config_path.startswith('.env'):
                    file_type = 'env'
                    # 簡單解析環境變量
                    config_data = {}
                    for line in content.split('\n'):
                        if '=' in line and not line.strip().startswith('#'):
                            key, value = line.split('=', 1)
                            config_data[key.strip()] = value.strip()
            
            except Exception as e:
                logger.debug(f"解析配置文件 {config_path} 內容失敗: {e}")
            
            return {
                'path': config_path,
                'type': file_type,
                'size_bytes': file_size,
                'modified_time': modified_time.isoformat(),
                'is_parseable': config_data is not None,
                'key_count': len(config_data) if isinstance(config_data, dict) else 0
            }
            
        except Exception as e:
            logger.debug(f"分析配置文件 {config_path} 失敗: {e}")
            return None
    
    async def _detect_network_services(self) -> Dict[str, Any]:
        """檢測網路服務"""
        network_services = {}
        
        try:
            # 檢測監聽端口
            listening_ports = []
            connections = psutil.net_connections(kind='inet')
            
            for conn in connections:
                if conn.status == 'LISTEN' and conn.laddr:
                    port_info = {
                        'port': conn.laddr.port,
                        'address': conn.laddr.ip,
                        'pid': conn.pid
                    }
                    
                    # 嘗試獲取進程信息
                    if conn.pid:
                        try:
                            proc = psutil.Process(conn.pid)
                            port_info['process_name'] = proc.name()
                            port_info['cmdline'] = proc.cmdline()
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass
                    
                    listening_ports.append(port_info)
            
            # 按端口排序
            listening_ports.sort(key=lambda x: x['port'])
            
            network_services['listening_ports'] = listening_ports
            network_services['total_listening_ports'] = len(listening_ports)
            
            # 檢測常見服務端口
            common_services = {
                5000: 'Flask/HTTP',
                5555: 'Dashboard',
                6379: 'Redis',
                8000: 'HTTP Server',
                8010: 'FastAPI',
                3306: 'MySQL',
                5432: 'PostgreSQL'
            }
            
            detected_services = {}
            for port_info in listening_ports:
                port = port_info['port']
                if port in common_services:
                    detected_services[port] = {
                        'service_type': common_services[port],
                        'port': port,
                        'address': port_info['address'],
                        'process_name': port_info.get('process_name', 'unknown')
                    }
            
            network_services['known_services'] = detected_services
            
        except Exception as e:
            logger.error(f"檢測網路服務失敗: {e}")
        
        return network_services
    
    def get_database_connections(self) -> List[str]:
        """獲取可用的資料庫連接"""
        if not self.detected_sources or 'databases' not in self.detected_sources:
            return []
        
        databases = self.detected_sources['databases']
        return [db_path for db_path, db_info in databases.items() if db_info.get('is_accessible', False)]
    
    def get_vendor_directories(self) -> Dict[str, str]:
        """獲取廠商目錄映射"""
        if not self.detected_sources or 'vendor_files' not in self.detected_sources:
            return {}
        
        vendor_files = self.detected_sources['vendor_files']
        vendor_dirs = {}
        
        for key, vendor_info in vendor_files.items():
            vendor_name = vendor_info.get('vendor_name')
            directory = vendor_info.get('directory')
            if vendor_name and directory:
                vendor_dirs[vendor_name] = directory
        
        return vendor_dirs
    
    def get_service_status(self, service_keyword: str) -> List[Dict[str, Any]]:
        """獲取特定服務的狀態"""
        if not self.detected_sources or 'services' not in self.detected_sources:
            return []
        
        services = self.detected_sources['services']
        matching_services = []
        
        for service_name, service_info in services.items():
            if service_info.get('keyword') == service_keyword:
                matching_services.append(service_info)
        
        return matching_services


# 全域實例
_data_source_detector: Optional[DataSourceDetector] = None


def get_data_source_detector() -> DataSourceDetector:
    """獲取數據源檢測器實例"""
    global _data_source_detector
    if _data_source_detector is None:
        _data_source_detector = DataSourceDetector()
    return _data_source_detector