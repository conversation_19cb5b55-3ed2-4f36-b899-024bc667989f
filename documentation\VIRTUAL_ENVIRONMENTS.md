# 虛擬環境比較表

## [BOARD] 可用的虛擬環境

| 虛擬環境 | Python 版本 | 啟動方式 | 執行腳本 | 推薦用途 |
|----------|------------|----------|----------|----------|
| `venv_win_3_11_12` | 3.11.12 | `activate_venv_3_11_12.bat` | `run_with_venv_3_11_12.bat script.py` | **推薦** (最新安全更新) |
| `venv` | 3.12.9 | `venv\Scripts\activate` | `venv\Scripts\python.exe script.py` | 傳統虛擬環境 |

## [ROCKET] 快速開始

### 推薦使用 Python 3.11.12 環境

```bash
# 啟動虛擬環境
activate_venv_3_11_12.bat

# 執行前端應用程式
run_with_venv_3_11_12.bat frontend/app.py

# 執行整合服務
run_with_venv_3_11_12.bat start_integrated_services.py
```

## [REFRESH] 版本差異

### Python 3.11.12 (推薦)
- **發布日期**: 2025年4月
- **安全更新**: 包含最新的安全修復
- **穩定性**: 更穩定的錯誤修復
- **狀態**: [OK] 已安裝，推薦使用

### Python 3.12.9 (傳統)
- **發布日期**: 2024年
- **狀態**: 系統預設，穩定版本

## [PACKAGE] 套件環境

### venv_win_3_11_12 (推薦)
包含完整的專案依賴套件：
- Flask 2.3.3
- FastAPI 0.104.1
- Playwright 1.52.0
- pandas 2.1.3
- SQLAlchemy 2.0.23
- 其他 48 個依賴套件

### venv (傳統)
基本 Python 環境，需要手動安裝套件

## [TARGET] 關於 Python 3.11.13

**注意**: Python 3.11.13 已於 2025年6月3日發布，但 uv 0.6.14 尚未支援。目前最高支援到 3.11.12。

### 如果需要 3.11.13：
1. 等待 uv 更新支援
2. 或使用傳統方式手動安裝：
   ```bash
   # 從 python.org 下載 3.11.13
   # 手動建立虛擬環境
   python -m venv venv_manual_3_11_13
   ```

## [TOOL] 環境管理

### 切換虛擬環境
```bash
# 使用 3.11.12 (推薦)
activate_venv_3_11_12.bat

# 使用傳統環境 (3.12.9)
venv\Scripts\activate
```

### 清理舊環境
```bash
# Python 3.11.6 環境已清理完成 [OK]
# 如需清理其他環境：
# rmdir /s venv_win_3_11_12  # 清理 3.11.12 環境
# rmdir /s venv              # 清理傳統環境
```

## [OK] 建議

**推薦使用 `venv_win_3_11_12`**，因為：
1. 包含最新的安全更新
2. 更好的穩定性
3. 與 3.11.13 差異極小

當 uv 支援 3.11.13 時，可以輕鬆升級。