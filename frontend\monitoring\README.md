# Monitoring Module - 監控功能模組

## 概述

監控功能模組負責處理系統的監控和健康檢查功能，提供即時的系統狀態監控、效能指標收集和警報管理。

## 功能特性

### 核心功能
- **系統監控儀表板** - 顯示系統整體健康狀態和關鍵指標
- **健康檢查** - 監控各個服務和組件的健康狀態
- **效能監控** - 收集和分析系統效能指標
- **警報管理** - 管理和配置系統警報規則

### 監控範圍
- **服務狀態** - Flask 服務 (Port 5000)、FastAPI 服務 (Port 8010)
- **數據庫狀態** - SQLite 數據庫連接和效能
- **任務隊列** - Dramatiq、Celery 隊列狀態
- **系統資源** - CPU、記憶體、磁碟使用率
- **業務指標** - 郵件處理量、數據品質指標

## 目錄結構

```
monitoring/
├── templates/               # HTML 模板
│   ├── system_dashboard.html # 系統監控儀表板
│   └── health_check.html    # 健康檢查
├── static/                  # 靜態資源
│   ├── css/
│   │   └── monitoring.css   # 監控樣式
│   ├── js/
│   │   ├── metrics.js       # 指標邏輯
│   │   ├── dashboard.js     # 儀表板邏輯
│   │   └── monitoring-api.js # 監控 API
│   └── images/              # 監控相關圖片
├── components/              # 可重用組件
│   ├── metric-card.html     # 指標卡片組件
│   ├── status-indicator.html # 狀態指示器
│   └── alert-panel.html     # 警報面板
├── routes/                  # 路由處理
│   └── monitoring_routes.py # 監控路由
└── README.md                # 本檔案
```

## API 端點

### 系統監控
- `GET /monitoring/dashboard` - 系統監控儀表板頁面
- `GET /api/monitoring/status` - 獲取系統整體狀態
- `GET /api/monitoring/metrics` - 獲取系統指標
- `GET /api/monitoring/services` - 獲取服務狀態

### 健康檢查
- `GET /monitoring/health` - 健康檢查頁面
- `GET /api/monitoring/health/all` - 獲取所有組件健康狀態
- `GET /api/monitoring/health/<component>` - 獲取特定組件健康狀態
- `POST /api/monitoring/health/check` - 執行健康檢查

### 效能監控
- `GET /api/monitoring/performance/cpu` - 獲取 CPU 使用率
- `GET /api/monitoring/performance/memory` - 獲取記憶體使用率
- `GET /api/monitoring/performance/disk` - 獲取磁碟使用率
- `GET /api/monitoring/performance/network` - 獲取網路使用率

### 警報管理
- `GET /api/monitoring/alerts` - 獲取警報列表
- `GET /api/monitoring/alerts/active` - 獲取活躍警報
- `POST /api/monitoring/alerts/acknowledge` - 確認警報
- `GET /api/monitoring/alerts/rules` - 獲取警報規則

### 即時監控
- `WebSocket /ws/monitoring/metrics` - 即時指標更新
- `WebSocket /ws/monitoring/alerts` - 即時警報通知
- `WebSocket /ws/monitoring/logs` - 即時日誌串流

## 資料模型

### SystemMetrics
- `metric_name`: 指標名稱
- `metric_value`: 指標值
- `metric_unit`: 指標單位
- `timestamp`: 時間戳記
- `component`: 組件名稱
- `tags`: 標籤 (JSON)

### ServiceStatus
- `service_name`: 服務名稱
- `status`: 服務狀態 (healthy, degraded, unhealthy, unknown)
- `response_time`: 回應時間 (毫秒)
- `last_check`: 最後檢查時間
- `error_message`: 錯誤訊息
- `uptime`: 運行時間

### Alert
- `alert_id`: 警報識別碼
- `alert_name`: 警報名稱
- `severity`: 嚴重程度 (info, warning, error, critical)
- `status`: 警報狀態 (active, acknowledged, resolved)
- `message`: 警報訊息
- `component`: 相關組件
- `triggered_at`: 觸發時間
- `acknowledged_at`: 確認時間
- `resolved_at`: 解決時間

### PerformanceMetrics
- `timestamp`: 時間戳記
- `cpu_usage`: CPU 使用率 (%)
- `memory_usage`: 記憶體使用率 (%)
- `disk_usage`: 磁碟使用率 (%)
- `network_in`: 網路輸入 (bytes/s)
- `network_out`: 網路輸出 (bytes/s)
- `active_connections`: 活躍連接數

## 監控組件

### 服務監控
- **Flask 服務** - 監控郵件處理服務 (Port 5000)
- **FastAPI 服務** - 監控 EQC 處理服務 (Port 8010)
- **數據庫服務** - 監控 SQLite 數據庫狀態
- **Redis 服務** - 監控 Redis 快取和訊息隊列

### 任務隊列監控
- **Dramatiq 隊列** - 監控任務隊列長度和處理速度
- **Celery 隊列** - 監控分散式任務執行狀態
- **工作者狀態** - 監控工作者健康狀態和負載

### 業務指標監控
- **郵件處理量** - 每小時/每日郵件處理統計
- **數據品質** - 解析成功率、數據完整性指標
- **用戶活動** - 用戶存取頻率和使用模式
- **錯誤率** - 系統錯誤和異常統計

## 警報規則

### 系統警報
- **CPU 使用率 > 80%** - 高 CPU 使用率警報
- **記憶體使用率 > 85%** - 高記憶體使用率警報
- **磁碟使用率 > 90%** - 磁碟空間不足警報
- **服務離線** - 關鍵服務不可用警報

### 業務警報
- **郵件處理失敗率 > 10%** - 郵件處理異常警報
- **任務隊列積壓 > 100** - 任務積壓警報
- **數據品質下降** - 解析成功率低於閾值警報
- **回應時間過長** - API 回應時間超過閾值警報

## 整合功能

### 統一監控儀表板
- **整合現有監控系統** - 與 `src/dashboard_monitoring/` 整合
- **即時數據更新** - 使用 WebSocket 提供即時更新
- **多維度視圖** - 提供系統、服務、業務多個維度的監控視圖
- **歷史數據分析** - 支援歷史數據查詢和趨勢分析

### 通知整合
- **多通道通知** - 支援郵件、LINE、Slack 等通知方式
- **通知規則** - 可配置的通知規則和頻率控制
- **升級機制** - 支援警報升級和通知升級
- **靜音功能** - 支援警報靜音和維護模式

## 效能優化

### 數據收集優化
- **採樣策略** - 使用適當的採樣頻率減少系統負載
- **數據聚合** - 對歷史數據進行聚合存儲
- **快取機制** - 使用 Redis 快取頻繁查詢的指標
- **異步處理** - 使用異步方式收集和處理監控數據

### 存儲優化
- **數據保留策略** - 設定不同精度數據的保留期限
- **壓縮存儲** - 對歷史數據進行壓縮存儲
- **分區存儲** - 按時間分區存儲監控數據
- **清理機制** - 定期清理過期的監控數據

## 開發注意事項

- 與現有 `src/dashboard_monitoring/` 系統整合
- 實作輕量級的監控代理，避免影響系統效能
- 支援可配置的監控頻率和警報閾值
- 確保監控系統本身的高可用性
- 提供豐富的視覺化圖表和儀表板
- 支援監控數據的導出和備份功能