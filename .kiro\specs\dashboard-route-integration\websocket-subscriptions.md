# Dashboard WebSocket 訂閱類型說明

## 概述

本文檔詳細說明 Dashboard 監控系統支援的 WebSocket 訂閱類型，以及每種類型提供的數據格式。

## 訂閱類型列表

### 1. metrics_update
**用途**: 接收所有監控指標的實時更新  
**更新頻率**: 每 5-10 秒  
**數據格式**:
```json
{
  "type": "metrics_update",
  "payload": {
    "email_processing": {
      "pending": 12,
      "processing": 3,
      "completed": 156,
      "failed": 2
    },
    "dramatiq_tasks": {
      "active": 5,
      "pending": 12,
      "workers": 3,
      "success_rate": 98.5
    },
    "system_resources": {
      "cpu": 45.2,
      "memory": 67.8,
      "disk": 42.1,
      "connections": 15
    }
  },
  "timestamp": "2025-08-08T11:20:00.000Z"
}
```

### 2. alert
**用途**: 接收系統告警和通知  
**觸發條件**: 系統異常、閾值超標、服務故障  
**數據格式**:
```json
{
  "type": "alert",
  "payload": {
    "id": "alert_001",
    "level": "warning",
    "message": "系統記憶體使用率超過 80%",
    "source": "system_monitor",
    "timestamp": "2025-08-08T11:20:00.000Z",
    "details": {
      "current_value": 85.2,
      "threshold": 80.0,
      "metric": "memory_usage"
    }
  }
}
```

### 3. system_status
**用途**: 接收系統狀態變化通知  
**觸發條件**: 服務啟動/停止、健康檢查狀態變化  
**數據格式**:
```json
{
  "type": "system_status",
  "payload": {
    "service": "email_processor",
    "status": "healthy",
    "previous_status": "warning",
    "uptime": "2h 15m",
    "last_check": "2025-08-08T11:20:00.000Z"
  }
}
```

### 4. email_metrics
**用途**: 接收郵件處理相關指標  
**更新頻率**: 每次郵件狀態變化時  
**數據格式**:
```json
{
  "type": "email_metrics",
  "payload": {
    "queue_status": {
      "pending": 15,
      "processing": 3,
      "completed": 180,
      "failed": 2
    },
    "processing_rate": 12.5,
    "average_processing_time": 45.2,
    "error_rate": 1.1
  }
}
```

### 5. dramatiq_metrics
**用途**: 接收 Dramatiq 任務隊列指標  
**更新頻率**: 每 10 秒  
**數據格式**:
```json
{
  "type": "dramatiq_metrics",
  "payload": {
    "workers": {
      "active": 3,
      "idle": 1,
      "total": 4
    },
    "tasks": {
      "active": 8,
      "pending": 25,
      "completed": 1250,
      "failed": 15
    },
    "task_types": {
      "code_comparison": {
        "active": 2,
        "pending": 8,
        "completed": 45,
        "failed": 1
      },
      "email_processing": {
        "active": 3,
        "pending": 12,
        "completed": 89,
        "failed": 2
      }
    }
  }
}
```

### 6. system_metrics
**用途**: 接收系統資源使用指標  
**更新頻率**: 每 5 秒  
**數據格式**:
```json
{
  "type": "system_metrics",
  "payload": {
    "cpu": {
      "usage_percent": 45.2,
      "load_average": [1.2, 1.5, 1.8]
    },
    "memory": {
      "usage_percent": 67.8,
      "available_gb": 8.2,
      "total_gb": 16.0
    },
    "disk": {
      "usage_percent": 42.1,
      "free_gb": 125.8,
      "total_gb": 250.0
    },
    "network": {
      "connections": 15,
      "bytes_sent": 1024000,
      "bytes_received": 2048000
    }
  }
}
```

### 7. file_metrics
**用途**: 接收檔案處理相關指標  
**更新頻率**: 每次檔案處理完成時  
**數據格式**:
```json
{
  "type": "file_metrics",
  "payload": {
    "processing_stats": {
      "files_processed": 156,
      "processing_rate": 25.3,
      "success_rate": 94.2,
      "average_size_mb": 2.8
    },
    "vendor_stats": {
      "GTK": {"files": 25, "success_rate": 96.0},
      "ETD": {"files": 32, "success_rate": 92.5},
      "JCET": {"files": 18, "success_rate": 94.4}
    }
  }
}
```

### 8. business_metrics
**用途**: 接收業務相關指標  
**更新頻率**: 每小時或業務事件觸發時  
**數據格式**:
```json
{
  "type": "business_metrics",
  "payload": {
    "daily_stats": {
      "mo_count": 45,
      "lot_count": 23,
      "quality_score": 95.2,
      "reports_generated": 8
    },
    "trends": {
      "mo_trend": "+12%",
      "quality_trend": "+2.1%"
    }
  }
}
```

### 9. network_metrics
**用途**: 接收網路監控指標  
**更新頻率**: 每 30 秒  
**數據格式**:
```json
{
  "type": "network_metrics",
  "payload": {
    "connectivity": {
      "redis_connected": true,
      "database_connected": true,
      "external_api_status": "healthy"
    },
    "performance": {
      "latency_ms": 25.3,
      "throughput_mbps": 100.5,
      "packet_loss_percent": 0.1
    }
  }
}
```

## 訂閱管理

### 訂閱請求
```javascript
// 訂閱多種類型
websocket.send(JSON.stringify({
  type: "subscribe",
  payload: {
    types: ["metrics_update", "alert", "system_status"]
  }
}));
```

### 取消訂閱
```javascript
websocket.send(JSON.stringify({
  type: "unsubscribe",
  payload: {
    types: ["alert"]
  }
}));
```

### 訂閱確認
```json
{
  "type": "connection_info",
  "payload": {
    "action": "subscribed",
    "subscriptions": ["metrics_update", "alert", "system_status"],
    "invalid_subscriptions": [],
    "total_subscriptions": 3
  },
  "client_id": "dashboard_xxxxx_timestamp"
}
```

## 錯誤處理

### 無效訂閱類型
如果嘗試訂閱不支援的類型，系統會在回應中標記為無效：
```json
{
  "type": "connection_info",
  "payload": {
    "action": "subscribed",
    "subscriptions": ["metrics_update"],
    "invalid_subscriptions": ["invalid_type"],
    "total_subscriptions": 1
  }
}
```

### 連接錯誤
```json
{
  "type": "error",
  "payload": {
    "code": "CONNECTION_ERROR",
    "message": "連接已中斷，正在嘗試重連...",
    "timestamp": "2025-08-08T11:20:00.000Z"
  }
}
```

## 最佳實踐

1. **選擇性訂閱**: 只訂閱需要的數據類型以減少網路流量
2. **錯誤處理**: 實作重連機制處理網路中斷
3. **數據緩存**: 在前端緩存最新數據以提供更好的用戶體驗
4. **性能監控**: 監控 WebSocket 連接狀態和數據更新頻率

## 更新歷史

- **2025-08-08**: 修復訂閱類型不匹配問題，添加所有前端期望的訂閱類型
- **2025-08-08**: 改進錯誤處理機制，提供詳細的訂閱狀態回饋
- **2025-08-08**: 完善文檔，添加所有訂閱類型的詳細說明和數據格式