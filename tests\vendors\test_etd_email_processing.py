#!/usr/bin/env python3
"""
測試ETD郵件處理 - 使用實際的郵件主旨
主旨: ANF to TE (GMT/G2735KS1U-K(BA-TEMP)/OS25070985/FT_FT1_B(5S)/GHKR03.14/FA2580338/F2570474A)
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加項目根目錄到路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

def test_etd_email_parsing():
    """測試ETD郵件解析"""
    logger.info("🧪 測試ETD郵件解析")
    
    try:
        from src.infrastructure.parsers.etd_parser import ETDParser
        from src.data_models.email_models import EmailData
        
        # 創建測試郵件數據
        email_data = EmailData(
            id=1,
            subject="ANF to TE (GMT/G2735KS1U-K(BA-TEMP)/OS25070985/FT_FT1_B(5S)/GHKR03.14/FA2580338/F2570474A)",
            sender="<EMAIL>",
            body="""
            Test email body with some data:
            Input Quantity: 1000
            Yield: 95.5%
            異常: None detected
            """,
            received_time=datetime.now(),
            attachments=[]
        )
        
        # 創建解析器
        parser = ETDParser()
        
        # 測試廠商識別
        identification = parser.identify_vendor(email_data)
        logger.info(f"廠商識別結果: {identification}")
        
        # 測試郵件解析
        result = parser.parse_email(email_data)
        logger.info(f"解析結果: {result}")
        
        # 分析主旨解析
        anf_data = parser.parse_anf_subject(email_data.subject)
        logger.info(f"ANF主旨解析: {anf_data}")
        
        # 顯示解析出的關鍵信息
        logger.info("📋 解析出的關鍵信息:")
        logger.info(f"  廠商代碼: {result.vendor_code}")
        logger.info(f"  產品代碼(PD): {result.product_code}")
        logger.info(f"  MO編號: {result.mo_number}")
        logger.info(f"  LOT編號: {result.lot_number}")
        
        if result.extracted_data:
            logger.info(f"  數量: {result.extracted_data.get('in_qty', 'N/A')}")
            logger.info(f"  良率: {result.extracted_data.get('yield_value', 'N/A')}")
            logger.info(f"  異常: {result.extracted_data.get('issue_description', 'N/A')}")
        
        return result.is_success
        
    except Exception as e:
        logger.error(f"❌ ETD郵件解析測試失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_etd_file_handler():
    """測試ETD檔案處理器"""
    logger.info("🧪 測試ETD檔案處理器")
    
    try:
        from src.infrastructure.adapters.file_handlers.etd_file_handler import ETDFileHandler
        
        # 創建檔案處理器
        base_path = r"\\************\test_log"
        handler = ETDFileHandler(base_path)
        
        # 測試參數 (從解析結果中獲得)
        pd = "G2735KS1U-K(BA-TEMP)"  # 產品代碼
        lot = "GHKR03.14"            # LOT編號
        mo = "F2570474A"             # MO編號
        temp_path = r"D:\temp\test_etd_download"
        
        logger.info(f"測試參數:")
        logger.info(f"  PD: {pd}")
        logger.info(f"  LOT: {lot}")
        logger.info(f"  MO: {mo}")
        logger.info(f"  目標路徑: {temp_path}")
        
        # 獲取來源路徑
        source_paths = handler.get_source_paths(pd, lot, mo)
        logger.info(f"ETD來源路徑:")
        for i, path in enumerate(source_paths, 1):
            logger.info(f"  路徑{i}: {path}")
        
        # 測試檔案複製 (模擬)
        logger.info("📁 模擬檔案複製...")
        
        # 創建測試目標目錄
        Path(temp_path).mkdir(parents=True, exist_ok=True)
        
        # 注意：這裡不實際執行複製，因為遠端路徑可能不存在
        logger.info("✅ ETD檔案處理器測試完成 (模擬)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ETD檔案處理器測試失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        return False

def test_complete_etd_pipeline():
    """測試完整的ETD處理管道"""
    logger.info("🧪 測試完整ETD處理管道")
    
    try:
        from src.tasks.pipeline_tasks import process_vendor_files_task
        
        # 準備完整的測試數據
        test_data = {
            'vendor_code': 'ETD',
            'mo': 'F2570474A',
            'temp_path': r'D:\temp\test_etd_pipeline',
            'pd': 'G2735KS1U-K(BA-TEMP)',
            'lot': 'GHKR03.14',
            'email_subject': 'ANF to TE (GMT/G2735KS1U-K(BA-TEMP)/OS25070985/FT_FT1_B(5S)/GHKR03.14/FA2580338/F2570474A)',
            'email_body': 'Test email body with Input Quantity: 1000, Yield: 95.5%',
            'pipeline_context': {
                'pipeline_id': 'test_etd_pipeline',
                'event_id': 'test_etd_event',
                'trigger_reason': 'manual_test',
                'triggered_at': datetime.now().isoformat()
            }
        }
        
        logger.info("📤 發送ETD測試任務到Dramatiq隊列...")
        
        # 發送任務
        message = process_vendor_files_task.send(**test_data)
        
        logger.info(f"✅ ETD測試任務已發送，消息ID: {message.message_id if hasattr(message, 'message_id') else 'unknown'}")
        logger.info("⏳ 請檢查Dramatiq worker日誌以查看處理結果")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ETD管道測試失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        return False

def analyze_download_mechanism():
    """分析下載機制問題"""
    logger.info("🔍 分析下載機制問題")
    
    logger.info("📋 當前ETD下載機制分析:")
    logger.info("1. 路徑結構:")
    logger.info("   - 主要路徑: \\\\************\\test_log\\ETD\\FT\\{PD}\\")
    logger.info("   - 備用路徑: \\\\************\\test_log\\Etrend\\FT\\{PD}\\{LOT}\\")
    
    logger.info("2. 當前問題:")
    logger.info("   ❌ 路徑結構可能不正確")
    logger.info("   ❌ 缺少LOT層級在主要路徑中")
    logger.info("   ❌ 檔案搜尋邏輯可能有問題")
    
    logger.info("3. 建議修復:")
    logger.info("   ✅ 修正路徑結構為: \\\\************\\test_log\\ETD\\FT\\{PD}\\{LOT}\\")
    logger.info("   ✅ 改進檔案搜尋邏輯")
    logger.info("   ✅ 添加更好的錯誤處理")
    
    return True

def main():
    """主測試函數"""
    logger.info("🚀 開始ETD郵件處理測試")
    
    results = []
    
    # 測試1: ETD郵件解析
    logger.info("\n" + "="*50)
    result1 = test_etd_email_parsing()
    results.append(("ETD郵件解析", result1))
    
    # 測試2: ETD檔案處理器
    logger.info("\n" + "="*50)
    result2 = test_etd_file_handler()
    results.append(("ETD檔案處理器", result2))
    
    # 測試3: 分析下載機制
    logger.info("\n" + "="*50)
    result3 = analyze_download_mechanism()
    results.append(("下載機制分析", result3))
    
    # 測試4: 完整ETD管道
    logger.info("\n" + "="*50)
    result4 = test_complete_etd_pipeline()
    results.append(("ETD完整管道", result4))
    
    # 總結
    logger.info("\n" + "="*50)
    logger.info("📊 ETD測試結果總結:")
    passed = 0
    for name, result in results:
        status = "✅ 成功" if result else "❌ 失敗"
        logger.info(f"  {name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{len(results)} 項測試成功")

if __name__ == "__main__":
    main()
