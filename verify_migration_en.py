#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 3.2 Static Resource Migration Verification Script
Verify all static resources have been correctly migrated and template references are correct
"""

import os
import re
import json
from pathlib import Path
from collections import defaultdict

class MigrationVerifier:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.frontend_root = self.project_root / "frontend"
        self.src_root = self.project_root / "src"
        self.errors = []
        self.warnings = []
        self.stats = {
            "css_files": 0,
            "js_files": 0, 
            "image_files": 0,
            "template_files": 0,
            "missing_files": 0,
            "broken_references": 0
        }
        
    def log_error(self, message):
        self.errors.append(message)
        print(f"[ERROR] {message}")
        
    def log_warning(self, message):
        self.warnings.append(message)
        print(f"[WARNING] {message}")
        
    def log_success(self, message):
        print(f"[SUCCESS] {message}")
        
    def log_info(self, message):
        print(f"[INFO] {message}")
        
    def verify_old_location_cleared(self):
        """Verify original location has been cleared"""
        print("\n=== 1. Verify Original Location Cleared ===")
        old_static_path = self.src_root / "dashboard_monitoring" / "static"
        
        if not old_static_path.exists():
            self.log_error(f"Original static directory does not exist: {old_static_path}")
            return False
            
        # Check if only __init__.py and directory structure remain
        for root, dirs, files in os.walk(old_static_path):
            for file in files:
                if file != "__init__.py":
                    file_path = Path(root) / file
                    self.log_error(f"File still exists in original location: {file_path}")
                    return False
                    
        self.log_success("Original location properly cleared")
        return True
        
    def scan_static_files(self):
        """Scan static files in frontend directory"""
        print("\n=== 2. Scan Frontend Static Files ===")
        
        if not self.frontend_root.exists():
            self.log_error(f"Frontend directory does not exist: {self.frontend_root}")
            return {}
            
        static_files = {}
        
        for module_dir in self.frontend_root.iterdir():
            if not module_dir.is_dir() or module_dir.name.startswith('.'):
                continue
                
            static_dir = module_dir / "static"
            if not static_dir.exists():
                continue
                
            module_files = {
                'css': [],
                'js': [],
                'images': [],
                'lib': []
            }
            
            # Scan CSS files
            css_dir = static_dir / "css"
            if css_dir.exists():
                for css_file in css_dir.glob("*.css"):
                    module_files['css'].append(css_file)
                    self.stats['css_files'] += 1
                    
            # Scan JS files
            js_dir = static_dir / "js"
            if js_dir.exists():
                for js_file in js_dir.rglob("*.js"):
                    module_files['js'].append(js_file)
                    self.stats['js_files'] += 1
                    
            # Scan image files
            images_dir = static_dir / "images"
            if images_dir.exists():
                for img_file in images_dir.rglob("*"):
                    if img_file.is_file():
                        module_files['images'].append(img_file)
                        self.stats['image_files'] += 1
                        
            # Scan lib files
            lib_dir = static_dir / "lib"
            if lib_dir.exists():
                for lib_file in lib_dir.rglob("*"):
                    if lib_file.is_file():
                        module_files['lib'].append(lib_file)
                        
            static_files[module_dir.name] = module_files
            
        # Print statistics
        self.log_info(f"Found static files:")
        self.log_info(f"  - CSS files: {self.stats['css_files']}")
        self.log_info(f"  - JS files: {self.stats['js_files']}")  
        self.log_info(f"  - Image files: {self.stats['image_files']}")
        
        return static_files
        
    def extract_template_references(self):
        """Extract static resource references from template files"""
        print("\n=== 3. Extract Template References ===")
        
        template_refs = defaultdict(list)
        url_for_pattern = re.compile(r"url_for\s*\(\s*['\"]([^'\"]+)\.static['\"],\s*filename\s*=\s*['\"]([^'\"]+)['\"]")
        
        for template_file in self.frontend_root.rglob("*.html"):
            self.stats['template_files'] += 1
            
            try:
                content = template_file.read_text(encoding='utf-8')
                matches = url_for_pattern.findall(content)
                
                for blueprint, filename in matches:
                    template_refs[template_file].append({
                        'blueprint': blueprint,
                        'filename': filename,
                        'expected_path': self.frontend_root / blueprint / "static" / filename
                    })
                    
            except Exception as e:
                self.log_warning(f"Failed to read template file: {template_file} - {e}")
                
        self.log_info(f"Scanned {self.stats['template_files']} template files")
        return dict(template_refs)
        
    def verify_template_references(self, template_refs):
        """Verify that files referenced in templates exist"""
        print("\n=== 4. Verify Template Reference Files Exist ===")
        
        all_refs_valid = True
        
        for template_file, refs in template_refs.items():
            for ref in refs:
                expected_path = ref['expected_path']
                
                if not expected_path.exists():
                    self.log_error(f"Missing file: {expected_path}")
                    self.log_error(f"  Referenced in: {template_file}")
                    self.log_error(f"  Blueprint: {ref['blueprint']}, File: {ref['filename']}")
                    self.stats['missing_files'] += 1
                    all_refs_valid = False
                else:
                    # Check if file is empty or just a placeholder
                    try:
                        file_size = expected_path.stat().st_size
                        if file_size < 50:  # Less than 50 bytes might be placeholder
                            content = expected_path.read_text(encoding='utf-8')
                            if 'placeholder' in content.lower() or content.strip().startswith('#'):
                                self.log_warning(f"Placeholder file: {expected_path}")
                    except:
                        pass
                        
        if all_refs_valid:
            self.log_success("All template-referenced files exist")
        else:
            self.log_error(f"Found {self.stats['missing_files']} missing files")
            
        return all_refs_valid
        
    def verify_key_migrated_files(self):
        """Verify key migrated files"""
        print("\n=== 5. Verify Key Migrated Files ===")
        
        key_files = [
            "frontend/monitoring/static/css/dashboard_components.css",
            "frontend/monitoring/static/css/dashboard_main.css",
            "frontend/monitoring/static/js/dashboard_charts.js", 
            "frontend/monitoring/static/js/dashboard_main.js",
            "frontend/monitoring/static/js/dashboard_websocket.js",
            "frontend/monitoring/static/images/favicon.ico"
        ]
        
        all_exist = True
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                size = full_path.stat().st_size
                self.log_success(f"Exists: {file_path} ({size} bytes)")
                
                # Check file content quality
                if size < 100:
                    self.log_warning(f"File might be placeholder: {file_path}")
                elif file_path.endswith('.css') and size > 10000:
                    self.log_success(f"CSS file has rich content: {file_path}")
                elif file_path.endswith('.js') and size > 5000:
                    self.log_success(f"JS file has rich content: {file_path}")
            else:
                self.log_error(f"Missing key file: {file_path}")
                all_exist = False
                
        return all_exist
        
    def check_file_contents(self):
        """Check file content quality"""
        print("\n=== 6. Check File Content Quality ===")
        
        # Check important CSS files
        css_files = [
            self.frontend_root / "shared" / "static" / "css" / "global.css",
            self.frontend_root / "monitoring" / "static" / "css" / "dashboard_main.css"
        ]
        
        for css_file in css_files:
            if css_file.exists():
                try:
                    content = css_file.read_text(encoding='utf-8')
                    css_rules = content.count('{')
                    if css_rules > 50:
                        self.log_success(f"CSS file has rich content: {css_file.name} ({css_rules} rules)")
                    elif css_rules > 10:
                        self.log_info(f"CSS file has basic content: {css_file.name} ({css_rules} rules)")
                    else:
                        self.log_warning(f"CSS file has limited content: {css_file.name} ({css_rules} rules)")
                except:
                    self.log_warning(f"Cannot read CSS file: {css_file}")
                    
        # Check important JS files
        js_files = [
            self.frontend_root / "shared" / "static" / "js" / "main.js",
            self.frontend_root / "monitoring" / "static" / "js" / "dashboard_main.js"
        ]
        
        for js_file in js_files:
            if js_file.exists():
                try:
                    content = js_file.read_text(encoding='utf-8')
                    functions = content.count('function ')
                    classes = content.count('class ')
                    total_constructs = functions + classes
                    
                    if total_constructs > 20:
                        self.log_success(f"JS file has complete functionality: {js_file.name} ({total_constructs} functions/classes)")
                    elif total_constructs > 5:
                        self.log_info(f"JS file has basic functionality: {js_file.name} ({total_constructs} functions/classes)")
                    else:
                        self.log_warning(f"JS file has limited functionality: {js_file.name} ({total_constructs} functions/classes)")
                except:
                    self.log_warning(f"Cannot read JS file: {js_file}")
                    
    def test_flask_import(self):
        """Test if Flask application can be imported normally"""
        print("\n=== 7. Test Flask Application Import ===")
        
        try:
            # Check if app.py exists
            app_file = self.frontend_root / "app.py"
            if app_file.exists():
                self.log_success("Frontend app.py file exists")
                
                # Try to read and simple validation
                content = app_file.read_text(encoding='utf-8')
                if 'Flask' in content and 'Blueprint' in content:
                    self.log_success("app.py contains Flask and Blueprint imports")
                    return True
                else:
                    self.log_warning("app.py might be missing necessary imports")
                    
            else:
                self.log_error("Frontend app.py file does not exist")
                
        except Exception as e:
            self.log_error(f"Flask application import test failed: {e}")
            
        return False
        
    def generate_report(self):
        """Generate verification report"""
        print("\n" + "="*60)
        print("Task 3.2 Static Resource Migration Verification Report")
        print("="*60)
        
        # Statistics
        print(f"\nStatistics:")
        print(f"  - CSS files: {self.stats['css_files']}")
        print(f"  - JavaScript files: {self.stats['js_files']}")
        print(f"  - Image files: {self.stats['image_files']}")
        print(f"  - Template files: {self.stats['template_files']}")
        print(f"  - Missing files: {self.stats['missing_files']}")
        
        # Error count
        print(f"\nVerification Results:")
        print(f"  - Error count: {len(self.errors)}")
        print(f"  - Warning count: {len(self.warnings)}")
        
        # Score calculation
        total_checks = 7
        passed_checks = total_checks - len(self.errors)
        score = max(0, (passed_checks / total_checks) * 10)
        
        print(f"\nFinal Score: {score:.1f}/10.0")
        
        # Status determination
        if len(self.errors) == 0 and self.stats['missing_files'] == 0:
            status = "COMPLETE SUCCESS"
            recommendation = "Can proceed to next task"
        elif len(self.errors) <= 2:
            status = "BASIC SUCCESS"  
            recommendation = "Recommend fixing minor issues before proceeding"
        else:
            status = "NEEDS FIXES"
            recommendation = "Must fix issues before continuing"
            
        print(f"\nVerification Status: {status}")
        print(f"Recommendation: {recommendation}")
        
        # Detailed errors
        if self.errors:
            print(f"\nError List:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
                
        if self.warnings:
            print(f"\nWarning List:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
                
        return {
            'score': score,
            'status': status,
            'recommendation': recommendation,
            'errors': self.errors,
            'warnings': self.warnings,
            'stats': self.stats
        }
        
    def run_verification(self):
        """Run complete verification"""
        print("Starting Task 3.2 Static Resource Migration Verification")
        print(f"Project root directory: {self.project_root}")
        
        # Execute all verifications
        self.verify_old_location_cleared()
        static_files = self.scan_static_files()
        template_refs = self.extract_template_references()
        self.verify_template_references(template_refs)
        self.verify_key_migrated_files()
        self.check_file_contents()
        self.test_flask_import()
        
        # Generate report
        return self.generate_report()

if __name__ == "__main__":
    project_root = Path(__file__).parent
    verifier = MigrationVerifier(project_root)
    result = verifier.run_verification()
    
    # Save verification results
    report_file = project_root / "migration_verification_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
        
    print(f"\nDetailed report saved to: {report_file}")