"""
監控模組路由
處理所有監控相關的路由和 API 端點
"""

from flask import Blueprint, render_template, jsonify, request
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.logging.logger_manager import LoggerManager

# 創建藍圖
monitoring_bp = Blueprint('monitoring', __name__,
                          template_folder='../templates',
                          static_folder='../static',
                          static_url_path='/static/monitoring')

# 初始化日誌
logger = LoggerManager().get_logger("MonitoringRoutes")


@monitoring_bp.route('/')
@monitoring_bp.route('/dashboard')
def dashboard():
    """系統監控儀表板主頁"""
    try:
        from datetime import datetime

        # 提供模板所需的變數
        system_health = {
            'status': 'healthy',
            'status_display': '系統正常',
            'score': 95
        }
        last_updated = datetime.now()

        resources = {
            'cpu_usage': 25.5,
            'cpu_cores': 8,
            'load_average': 1.2,
            'cpu_frequency': 2400,
            'memory_usage_percent': 45.2,
            'memory_used_gb': 7.2,
            'memory_total_gb': 16.0,
            'memory_available_gb': 8.8,
            'disk_usage_percent': 60.5,
            'disk_used_gb': 120.5,
            'disk_total_gb': 200.0,
            'disk_free_gb': 79.5,
            'network_status': '正常',
            'network_sent_mbps': 2.5,
            'network_recv_mbps': 5.8,
            'network_sent_gb': 15.2,
            'network_recv_gb': 45.8
        }

        app_status = {
            'healthy_services': 5,
            'warning_services': 1,
            'critical_services': 0
        }

        services = [
            {
                'id': 'email_service',
                'name': '郵件服務',
                'status': 'healthy',
                'status_display': '正常',
                'icon': '📧',
                'uptime_percent': 99.9,
                'response_time': 120,
                'last_check': datetime.now()
            },
            {
                'id': 'database',
                'name': '資料庫',
                'status': 'healthy',
                'status_display': '正常',
                'icon': '🗄️',
                'uptime_percent': 99.8,
                'response_time': 45,
                'last_check': datetime.now()
            },
            {
                'id': 'file_system',
                'name': '檔案系統',
                'status': 'healthy',
                'status_display': '正常',
                'icon': '📁',
                'uptime_percent': 100.0,
                'response_time': 30,
                'last_check': datetime.now()
            },
            {
                'id': 'task_queue',
                'name': '任務佇列',
                'status': 'warning',
                'status_display': '警告',
                'icon': '⚠️',
                'uptime_percent': 98.5,
                'response_time': 200,
                'last_check': datetime.now()
            },
            {
                'id': 'api_service',
                'name': 'API 服務',
                'status': 'healthy',
                'status_display': '正常',
                'icon': '🔗',
                'uptime_percent': 99.7,
                'response_time': 85,
                'last_check': datetime.now()
            },
            {
                'id': 'monitoring',
                'name': '監控系統',
                'status': 'healthy',
                'status_display': '正常',
                'icon': '📊',
                'uptime_percent': 99.9,
                'response_time': 60,
                'last_check': datetime.now()
            }
        ]

        alerts = {
            'critical_count': 0,
            'warning_count': 1,
            'info_count': 2
        }

        active_alerts = [
            {
                'id': 'alert_001',
                'severity': 'warning',
                'title': '任務佇列延遲',
                'message': '任務佇列回應時間超過正常範圍',
                'created_at': datetime.now(),
                'source': '任務佇列'
            },
            {
                'id': 'alert_002',
                'severity': 'info',
                'title': '系統更新可用',
                'message': '有新的系統更新可供安裝',
                'created_at': datetime.now(),
                'source': '系統'
            },
            {
                'id': 'alert_003',
                'severity': 'info',
                'title': '備份完成',
                'message': '每日備份已成功完成',
                'created_at': datetime.now(),
                'source': '備份系統'
            }
        ]

        recent_events = [
            {
                'id': 'event_001',
                'type': 'info',
                'type_display': '資訊',
                'title': '系統啟動',
                'description': '系統成功啟動並載入所有服務',
                'timestamp': datetime.now(),
                'source': '系統'
            },
            {
                'id': 'event_002',
                'type': 'success',
                'type_display': '成功',
                'title': '備份完成',
                'description': '每日自動備份已成功完成',
                'timestamp': datetime.now(),
                'source': '備份服務'
            }
        ]

        system_info = {
            'os_name': 'Windows',
            'os_version': '11',
            'hostname': 'DESKTOP-PC',
            'boot_time': datetime.now().replace(hour=8, minute=0, second=0),
            'uptime': '8 小時 15 分鐘'
        }

        python_info = {
            'version': '3.11.12',
            'virtual_env': 'venv_win_3_11_12',
            'package_count': 45,
            'working_directory': 'D:\\project\\python\\outlook_summary'
        }

        app_info = {
            'version': '1.0.0',
            'build_time': datetime.now().replace(hour=10, minute=30),
            'config_mode': '開發模式'
        }

        thresholds = {
            'cpu_warning': 75,
            'cpu_critical': 90,
            'memory_warning': 80,
            'memory_critical': 95
        }

        notifications = {
            'email_enabled': True,
            'webhook_enabled': False,
            'desktop_enabled': True
        }

        return render_template('system_dashboard.html',
                             system_health=system_health,
                             last_updated=last_updated,
                             resources=resources,
                             app_status=app_status,
                             services=services,
                             alerts=alerts,
                             active_alerts=active_alerts,
                             recent_events=recent_events,
                             system_info=system_info,
                             python_info=python_info,
                             app_info=app_info,
                             thresholds=thresholds,
                             notifications=notifications)
    except Exception as e:
        logger.error(f"載入監控儀表板失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@monitoring_bp.route('/health')
def health():
    """健康檢查頁面"""
    try:
        return render_template('health_check.html')
    except Exception as e:
        logger.error(f"載入健康檢查頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


# API 路由
@monitoring_bp.route('/api/status')
def api_status():
    """獲取系統整體狀態 API"""
    try:
        # TODO: 實作系統狀態邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'system_status': 'healthy',
                'uptime': '0d 0h 0m',
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0
            }
        })
    except Exception as e:
        logger.error(f"獲取系統狀態失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@monitoring_bp.route('/api/metrics')
def api_metrics():
    """獲取系統指標 API"""
    try:
        # TODO: 實作系統指標邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'timestamp': '2025-01-08T00:00:00Z',
                'metrics': []
            }
        })
    except Exception as e:
        logger.error(f"獲取系統指標失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@monitoring_bp.route('/api/health/all')
def api_health_all():
    """獲取所有組件健康狀態 API"""
    try:
        # TODO: 實作健康檢查邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'flask_service': 'healthy',
                'fastapi_service': 'healthy',
                'database': 'healthy',
                'redis': 'healthy'
            }
        })
    except Exception as e:
        logger.error(f"獲取健康狀態失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@monitoring_bp.route('/api/alerts')
def api_alerts():
    """獲取警報列表 API"""
    try:
        # TODO: 實作警報邏輯
        return jsonify({
            'status': 'success',
            'data': []
        })
    except Exception as e:
        logger.error(f"獲取警報列表失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


# 資料庫管理路由
@monitoring_bp.route('/database-manager')
def database_manager():
    """資料庫管理頁面"""
    try:
        import os
        # 初始化資料庫連接
        from src.infrastructure.adapters.database.email_database import EmailDatabase
        database = EmailDatabase()
        db_path = os.path.abspath(getattr(database, 'database_url', 'email_inbox.db').replace('sqlite:///', ''))
        return render_template('database_manager.html', db_path=db_path)
    except Exception as e:
        logger.error(f"載入資料庫管理頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


# 資料庫管理 API
@monitoring_bp.route('/api/database/info')
def api_database_info():
    """獲取資料庫資訊"""
    try:
        import os
        from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
        from src.infrastructure.adapters.database.email_database import EmailDatabase
        
        database = EmailDatabase()
        db_path = getattr(database, 'database_url', 'email_inbox.db').replace('sqlite:///', '')
        db_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
        
        with database.get_session() as session:
            tables_info = {
                'emails': session.query(EmailDB).count(),
                'senders': session.query(SenderDB).count(),
                'attachments': session.query(AttachmentDB).count(),
                'email_process_status': session.query(EmailProcessStatusDB).count()
            }
        
        return jsonify({
            'success': True,
            'data': {
                'db_path': db_path,
                'db_size': db_size,
                'tables': tables_info
            }
        })
    except Exception as e:
        logger.error(f"獲取資料庫資訊失敗: {e}")
        return jsonify({'success': False, 'error': str(e)})


@monitoring_bp.route('/api/database/table/<table_name>')
def api_get_table_data(table_name: str):
    """獲取表格資料"""
    try:
        from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
        from src.infrastructure.adapters.database.email_database import EmailDatabase
        
        # 映射表名到模型
        table_models = {
            'emails': EmailDB,
            'senders': SenderDB,
            'attachments': AttachmentDB,
            'email_process_status': EmailProcessStatusDB
        }
        
        if table_name not in table_models:
            return jsonify({'success': False, 'error': '無效的表名'})
        
        model = table_models[table_name]
        limit = int(request.args.get('limit', 100))  # 預設減少到100筆
        offset = int(request.args.get('offset', 0))
        
        # 針對 email_process_status 表格，進一步限制預設顯示數量
        if table_name == 'email_process_status':
            limit = int(request.args.get('limit', 50))  # 更少的預設顯示
        
        database = EmailDatabase()
        with database.get_session() as session:
            query = session.query(model)
            
            # 針對 email_process_status 表格，按時間倒序排列
            if table_name == 'email_process_status' and hasattr(model, 'started_at'):
                query = query.order_by(model.started_at.desc())
            elif hasattr(model, 'created_at'):
                query = query.order_by(model.created_at.desc())
            elif hasattr(model, 'id'):
                query = query.order_by(model.id.desc())
            
            total = query.count()
            records = query.offset(offset).limit(limit).all()
            
            # 轉換為字典
            data = []
            for record in records:
                row = {}
                for column in model.__table__.columns:
                    value = getattr(record, column.name)
                    # 處理日期時間
                    if hasattr(value, 'isoformat'):
                        value = value.isoformat()
                    elif hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool)):
                        value = str(value)
                    row[column.name] = value
                data.append(row)
            
            # 獲取欄位資訊
            columns = [{'name': col.name, 'type': str(col.type)} for col in model.__table__.columns]
            
        return jsonify({
            'success': True,
            'data': {
                'records': data,
                'columns': columns,
                'total': total,
                'offset': offset,
                'limit': limit
            }
        })
        
    except Exception as e:
        logger.error(f"獲取表格資料失敗: {e}")
        return jsonify({'success': False, 'error': str(e)})


@monitoring_bp.route('/api/database/execute', methods=['POST'])
def api_execute_query():
    """執行 SQL 查詢（僅限 SELECT）"""
    try:
        from src.infrastructure.adapters.database.email_database import EmailDatabase
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        # 安全檢查：只允許 SELECT 語句
        if not query.upper().startswith('SELECT'):
            return jsonify({'success': False, 'error': '只允許執行 SELECT 查詢'})
        
        # 禁止危險關鍵字
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'CREATE', 'ALTER', 'TRUNCATE']
        for keyword in dangerous_keywords:
            if keyword in query.upper():
                return jsonify({'success': False, 'error': f'不允許使用 {keyword} 關鍵字'})
        
        database = EmailDatabase()
        with database.get_session() as session:
            from sqlalchemy import text
            result = session.execute(text(query))

            # 獲取欄位名稱
            columns = list(result.keys()) if hasattr(result, 'keys') else []

            # 獲取資料
            rows = []
            for row in result:
                row_dict = {}
                for i, value in enumerate(row):
                    column_name = columns[i] if i < len(columns) else f'column_{i}'
                    if value is not None:
                        # 處理日期時間格式
                        if hasattr(value, 'isoformat'):
                            row_dict[column_name] = value.isoformat()
                        else:
                            row_dict[column_name] = value
                    else:
                        row_dict[column_name] = None
                rows.append(row_dict)

        return jsonify({
            'success': True,
            'data': {
                'columns': columns,
                'records': rows
            }
        })
        
    except Exception as e:
        logger.error(f"執行查詢失敗: {e}")
        return jsonify({'success': False, 'error': str(e)})


@monitoring_bp.route('/api/database/<table_name>/<int:record_id>')
def api_get_record_detail(table_name: str, record_id: int):
    """獲取單筆記錄詳情"""
    try:
        from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
        from src.infrastructure.adapters.database.email_database import EmailDatabase

        # 映射表名到模型
        table_models = {
            'emails': EmailDB,
            'senders': SenderDB,
            'attachments': AttachmentDB,
            'email_process_status': EmailProcessStatusDB
        }

        if table_name not in table_models:
            return jsonify({'success': False, 'error': '無效的表名'})

        model = table_models[table_name]
        database = EmailDatabase()

        with database.get_session() as session:
            record = session.query(model).filter_by(id=record_id).first()

            if not record:
                return jsonify({'success': False, 'error': '記錄不存在'})

            # 將記錄轉換為字典
            record_dict = {}
            for column in model.__table__.columns:
                value = getattr(record, column.name)
                if value is not None:
                    # 處理日期時間格式
                    if hasattr(value, 'isoformat'):
                        record_dict[column.name] = value.isoformat()
                    else:
                        record_dict[column.name] = value
                else:
                    record_dict[column.name] = None

            return jsonify({
                'success': True,
                'data': record_dict
            })

    except Exception as e:
        logger.error(f"獲取記錄詳情失敗: {e}")
        return jsonify({'success': False, 'error': str(e)})