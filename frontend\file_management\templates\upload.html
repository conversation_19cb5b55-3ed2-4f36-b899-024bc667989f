<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>檔案上傳 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('files.static', filename='css/file-manager.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="upload-container">
        <header class="upload-header">
            <h1>檔案上傳</h1>
            <div class="header-actions">
                <button id="clear-queue-btn" class="btn btn-danger">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">清空隊列</span>
                </button>
                <a href="{{ url_for('file-management.file_manager') }}" class="btn btn-outline">
                    <span class="btn-icon">↩</span>
                    <span class="btn-text">返回檔案管理器</span>
                </a>
            </div>
        </header>

        <div class="upload-content">
            <!-- 上傳區域 -->
            <div class="upload-zone-section">
                <div class="upload-zone" id="upload-zone">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <h3>拖拽檔案到此處上傳</h3>
                        <p>或點擊選擇檔案</p>
                        <small>支援多個檔案同時上傳，最大單檔 100MB</small>
                    </div>
                    <button class="btn btn-primary" id="select-files-btn">選擇檔案</button>
                    <input type="file" id="file-input" multiple hidden>
                </div>
            </div>

            <!-- 上傳設定 -->
            <div class="upload-settings">
                <div class="settings-card">
                    <h3>⚙️ 上傳設定</h3>
                    <div class="settings-grid">
                        <div class="setting-group">
                            <label for="upload-destination">上傳目標資料夾:</label>
                            <div class="destination-selector">
                                <input type="text" id="upload-destination" value="{{ current_path or '/uploads' }}" readonly>
                                <button class="btn btn-secondary" id="select-destination-btn">選擇資料夾</button>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="conflict-resolution">檔名衝突處理:</label>
                            <select id="conflict-resolution">
                                <option value="rename">自動重新命名</option>
                                <option value="overwrite">覆蓋現有檔案</option>
                                <option value="skip">跳過重複檔案</option>
                                <option value="ask">每次詢問</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label for="auto-extract">自動解壓縮:</label>
                            <select id="auto-extract">
                                <option value="none" selected>不解壓縮</option>
                                <option value="zip">解壓縮 ZIP 檔案</option>
                                <option value="all">解壓縮所有壓縮檔</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="create-subfolder" checked>
                                為每次上傳建立子資料夾
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="auto-process">
                                上傳完成後自動處理
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="notify-completion" checked>
                                上傳完成後通知
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 上傳隊列 -->
            <div class="upload-queue-section">
                <div class="queue-card">
                    <div class="queue-header">
                        <h3>📋 上傳隊列</h3>
                        <div class="queue-actions">
                            <button id="start-upload-btn" class="btn btn-primary" disabled>
                                <span class="btn-icon">▶️</span>
                                <span class="btn-text">開始上傳</span>
                            </button>
                            <button id="pause-upload-btn" class="btn btn-secondary" style="display: none;">
                                <span class="btn-icon">⏸️</span>
                                <span class="btn-text">暫停上傳</span>
                            </button>
                            <button id="resume-upload-btn" class="btn btn-primary" style="display: none;">
                                <span class="btn-icon">▶️</span>
                                <span class="btn-text">繼續上傳</span>
                            </button>
                        </div>
                    </div>

                    <div class="queue-stats">
                        <div class="stat-item">
                            <span class="stat-label">總檔案:</span>
                            <span class="stat-value" id="total-files">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">總大小:</span>
                            <span class="stat-value" id="total-size">0 MB</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已完成:</span>
                            <span class="stat-value" id="completed-files">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">剩餘時間:</span>
                            <span class="stat-value" id="estimated-time">-</span>
                        </div>
                    </div>

                    <div class="overall-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="overall-progress-fill" style="width: 0%;"></div>
                        </div>
                        <div class="progress-text">
                            <span id="overall-progress-text">0% 完成</span>
                            <span id="upload-speed">0 KB/s</span>
                        </div>
                    </div>

                    <div class="file-queue" id="file-queue">
                        <div class="empty-queue">
                            <div class="empty-icon">📭</div>
                            <p>上傳隊列為空</p>
                            <small>請選擇要上傳的檔案</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 上傳歷史 -->
            <div class="upload-history-section">
                <div class="history-card">
                    <div class="history-header">
                        <h3>📜 上傳歷史</h3>
                        <div class="history-actions">
                            <button id="clear-history-btn" class="btn btn-sm btn-danger">清除歷史</button>
                            <button id="export-history-btn" class="btn btn-sm btn-secondary">匯出記錄</button>
                        </div>
                    </div>

                    <div class="history-filters">
                        <div class="filter-group">
                            <label for="history-date-range">日期範圍:</label>
                            <select id="history-date-range">
                                <option value="today">今天</option>
                                <option value="week" selected>本週</option>
                                <option value="month">本月</option>
                                <option value="all">全部</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="history-status">狀態:</label>
                            <select id="history-status">
                                <option value="all" selected>全部</option>
                                <option value="success">成功</option>
                                <option value="failed">失敗</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <button id="apply-history-filters-btn" class="btn btn-sm btn-primary">套用篩選</button>
                    </div>

                    <div class="history-list" id="upload-history-list">
                        {% for upload in upload_history %}
                        <div class="history-item {{ upload.status }}">
                            <div class="history-info">
                                <div class="history-file">
                                    <span class="file-icon">{{ upload.file_icon }}</span>
                                    <span class="file-name">{{ upload.filename }}</span>
                                    <span class="file-size">{{ upload.size_formatted }}</span>
                                </div>
                                <div class="history-meta">
                                    <span class="upload-time">{{ upload.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    <span class="upload-status {{ upload.status }}">
                                        {% if upload.status == 'success' %}✅ 成功
                                        {% elif upload.status == 'failed' %}❌ 失敗
                                        {% elif upload.status == 'cancelled' %}⛔ 已取消
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="history-actions">
                                {% if upload.status == 'success' %}
                                <button class="btn btn-sm btn-outline" onclick="openFile('{{ upload.file_path }}')">開啟</button>
                                <button class="btn btn-sm btn-secondary" onclick="downloadFile('{{ upload.file_path }}')">下載</button>
                                {% elif upload.status == 'failed' %}
                                <button class="btn btn-sm btn-primary" onclick="retryUpload('{{ upload.id }}')">重試</button>
                                {% endif %}
                                <button class="btn btn-sm btn-danger" onclick="deleteUploadRecord('{{ upload.id }}')">刪除記錄</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="pagination" id="history-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 資料夾選擇模態框 -->
    <div class="modal" id="folder-select-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>選擇目標資料夾</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="folder-tree" id="folder-tree">
                    <!-- 資料夾樹狀結構會由JavaScript動態生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="confirm-destination-btn" class="btn btn-primary">確認選擇</button>
                <button class="btn btn-secondary" onclick="closeModal('folder-select-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('files.static', filename='js/components/file-upload.js') }}"></script>
</body>
</html>