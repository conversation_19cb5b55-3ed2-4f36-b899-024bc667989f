# Simple activation script that stays in current session
# Usage: . .\dev_env.ps1

# 設定中文編碼環境變數
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:LANG = "zh_TW.UTF-8"
Write-Host "設定中文編碼環境變數..." -ForegroundColor Green

if (Test-Path ".\venv_win_3_11_12\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Green
    . ".\venv_win_3_11_12\Scripts\Activate.ps1"
    
    # 設定 Flask 環境變數
    $env:FLASK_APP = "frontend.app:create_app"
    $env:FLASK_ENV = "development"
    $env:FLASK_DEBUG = "True"
    Write-Host "設定 Flask 環境變數..." -ForegroundColor Green
    
    Write-Host "Environment ready! Available commands:" -ForegroundColor Yellow
    Write-Host "  python frontend/app.py                    # 啟動前端應用程式" -ForegroundColor Cyan
    Write-Host "  flask run                                 # 使用 Flask CLI 啟動" -ForegroundColor Cyan
    Write-Host "  python start_integrated_services.py      # 啟動整合服務" -ForegroundColor Cyan
    Write-Host "  make help                                 # 查看所有可用命令" -ForegroundColor Cyan
    Write-Host "  pytest                                    # 執行測試" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Flask 配置:" -ForegroundColor Yellow
    Write-Host "  應用程式: $env:FLASK_APP" -ForegroundColor White
    Write-Host "  環境: $env:FLASK_ENV" -ForegroundColor White
    Write-Host "  除錯模式: $env:FLASK_DEBUG" -ForegroundColor White
    Write-Host "  編碼設定: UTF-8 ✅" -ForegroundColor Green
} else {
    Write-Host "Virtual environment not found!" -ForegroundColor Red
}