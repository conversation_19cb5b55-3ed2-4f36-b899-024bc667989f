"""
EQC 模組路由
處理所有 EQC 相關的路由和 API 端點
"""

from flask import Blueprint, render_template, jsonify, request, redirect
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.logging.logger_manager import LoggerManager

# 創建藍圖
eqc_bp = Blueprint('eqc', __name__,
                   template_folder='../templates',
                   static_folder='../static',
                   static_url_path='/static/eqc')

# 初始化日誌
logger = LoggerManager().get_logger("EQCRoutes")


@eqc_bp.route('/')
@eqc_bp.route('/dashboard')
def dashboard():
    """EQC 儀表板主頁"""
    try:
        return render_template('eqc_dashboard.html')
    except Exception as e:
        logger.error(f"載入 EQC 儀表板失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@eqc_bp.route('/quality-check')
def quality_check():
    """品質檢查頁面"""
    try:
        return render_template('quality_check.html')
    except Exception as e:
        logger.error(f"載入品質檢查頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@eqc_bp.route('/compliance')
def compliance():
    """合規檢查頁面"""
    try:
        return render_template('compliance.html')
    except Exception as e:
        logger.error(f"載入合規檢查頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


# FastAPI 服務整合路由
@eqc_bp.route('/ft-eqc')
def ft_eqc_redirect():
    """重定向到 FT-EQC 處理介面"""
    return redirect("http://localhost:8010/ui")


@eqc_bp.route('/ft-eqc-api')
def ft_eqc_api_redirect():
    """重定向到 FT-EQC API 文檔"""
    return redirect("http://localhost:8010/docs")


# API 路由
@eqc_bp.route('/api/metrics')
def api_metrics():
    """獲取 EQC 關鍵指標 API"""
    try:
        # TODO: 實作 EQC 指標邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'pass_rate': 0.0
            }
        })
    except Exception as e:
        logger.error(f"獲取 EQC 指標失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@eqc_bp.route('/api/quality/run', methods=['POST'])
def api_run_quality_check():
    """執行品質檢查 API"""
    try:
        # TODO: 實作品質檢查邏輯
        return jsonify({
            'status': 'success',
            'message': '品質檢查已啟動',
            'job_id': 'placeholder_job_id'
        })
    except Exception as e:
        logger.error(f"執行品質檢查失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@eqc_bp.route('/api/compliance/validate', methods=['POST'])
def api_validate_compliance():
    """執行合規驗證 API"""
    try:
        # TODO: 實作合規驗證邏輯
        return jsonify({
            'status': 'success',
            'message': '合規驗證已啟動',
            'job_id': 'placeholder_job_id'
        })
    except Exception as e:
        logger.error(f"執行合規驗證失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500