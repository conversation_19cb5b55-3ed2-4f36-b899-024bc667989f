"""
Dramatiq 重試追蹤器 - 解決重試次數檢測問題

🎯 核心功能：
1. 使用回調機制追蹤重試次數
2. 在適當時機觸發 LINE 通知
3. 提供可靠的重試狀態檢測

🔧 技術原理：
- 使用 Dramatiq 的 on_failure 回調
- 在 Redis/Memory 中存儲重試計數
- 實現跨任務實例的狀態共享
"""

import json
import time
from typing import Dict, Any, Optional
from datetime import datetime

from dramatiq import get_broker
from loguru import logger

# 導入通知服務
from src.services.vendor_file_notification import get_vendor_file_notification_service


class DramatiqRetryTracker:
    """Dramatiq 重試追蹤器"""
    
    def __init__(self):
        """初始化重試追蹤器"""
        self.broker = get_broker()
        
        # 強制初始化通知服務，確保實際發送 LINE 通知
        try:
            # 確保 .env 文件被載入
            from dotenv import load_dotenv
            load_dotenv()
            
            self.notification_service = get_vendor_file_notification_service()
            self.notification_available = True
            logger.info("✅ 通知服務初始化成功 - 將發送實際 LINE 通知")
        except Exception as e:
            logger.error(f"❌ 通知服務初始化失敗: {e}")
            logger.error("🔧 嘗試手動初始化通知服務...")
            
            # 手動嘗試初始化
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            
            # 強制設為不可用，避免後續錯誤
            self.notification_service = None
            self.notification_available = False  # 設為 False 以避免模擬模式
            logger.warning("⚠️ 通知服務初始化失敗，將不會發送通知")
        
        # 根據 broker 類型選擇存儲後端
        if hasattr(self.broker, 'client'):
            # Redis broker
            self.storage = RedisRetryStorage(self.broker.client)
            self.storage_type = "redis"
        else:
            # Memory broker
            self.storage = MemoryRetryStorage()
            self.storage_type = "memory"
        
        logger.info(f"🔧 Dramatiq 重試追蹤器初始化完成 - 存儲類型: {self.storage_type}, 通知可用: {self.notification_available}")
    
    def track_task_attempt(self, task_id: str, vendor_code: str, mo: str, 
                          attempt_number: int = 0, is_retry: bool = False) -> None:
        """
        追蹤任務嘗試
        
        Args:
            task_id: 任務ID
            vendor_code: 廠商代碼  
            mo: MO編號
            attempt_number: 嘗試次數
            is_retry: 是否為重試
        """
        retry_info = {
            'task_id': task_id,
            'vendor_code': vendor_code,
            'mo': mo,
            'attempt_number': attempt_number,
            'is_retry': is_retry,
            'timestamp': datetime.now().isoformat(),
            'last_updated': time.time()
        }
        
        self.storage.store_retry_info(task_id, retry_info)
        logger.debug(f"[RETRY_TRACKER] 記錄任務嘗試: {task_id} - attempt={attempt_number}, retry={is_retry}")
    
    def get_retry_count(self, task_id: str) -> tuple[int, bool]:
        """
        獲取任務的重試次數
        
        Args:
            task_id: 任務ID
            
        Returns:
            tuple[int, bool]: (重試次數, 是否找到記錄)
        """
        retry_info = self.storage.get_retry_info(task_id)
        
        if retry_info:
            attempt_number = retry_info.get('attempt_number', 0)
            logger.debug(f"[RETRY_TRACKER] 找到重試記錄: {task_id} - attempts={attempt_number}")
            return attempt_number, True
        else:
            logger.debug(f"[RETRY_TRACKER] 未找到重試記錄: {task_id}")
            return 0, False
    
    def should_send_notification(self, task_id: str, max_retries: int = 3) -> bool:
        """
        判斷是否應該發送通知
        
        Args:
            task_id: 任務ID
            max_retries: 最大重試次數
            
        Returns:
            bool: 是否應該發送通知
        """
        retry_count, found = self.get_retry_count(task_id)
        
        # 如果找不到記錄，保守起見發送通知
        if not found:
            logger.warning(f"[RETRY_TRACKER] 未找到重試記錄，保守發送通知: {task_id}")
            return True
        
        # 如果達到最大重試次數，發送通知
        should_notify = retry_count >= max_retries
        logger.info(f"[RETRY_TRACKER] 通知決策: {task_id} - retries={retry_count}/{max_retries}, notify={should_notify}")
        
        return should_notify
    
    def increment_retry_count(self, task_id: str) -> int:
        """
        增加重試計數
        
        Args:
            task_id: 任務ID
            
        Returns:
            int: 新的重試次數
        """
        retry_info = self.storage.get_retry_info(task_id) or {}
        new_count = retry_info.get('attempt_number', 0) + 1
        
        retry_info.update({
            'attempt_number': new_count,
            'is_retry': True,
            'timestamp': datetime.now().isoformat(),
            'last_updated': time.time()
        })
        
        self.storage.store_retry_info(task_id, retry_info)
        logger.info(f"[RETRY_TRACKER] 增加重試計數: {task_id} - new_count={new_count}")
        
        return new_count
    
    def cleanup_old_records(self, max_age_seconds: int = 3600) -> int:
        """
        清理舊記錄
        
        Args:
            max_age_seconds: 記錄最大保存時間（秒）
            
        Returns:
            int: 清理的記錄數量
        """
        return self.storage.cleanup_old_records(max_age_seconds)
    
    def cleanup_specific_record(self, task_id: str) -> bool:
        """
        清理特定任務的記錄
        
        Args:
            task_id: 任務ID
            
        Returns:
            bool: 是否成功清理
        """
        return self.storage.cleanup_specific_record(task_id)


class RetryStorage:
    """重試存儲基類"""
    
    def store_retry_info(self, task_id: str, retry_info: Dict[str, Any]) -> None:
        """存儲重試信息"""
        raise NotImplementedError
    
    def get_retry_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取重試信息"""
        raise NotImplementedError
    
    def cleanup_old_records(self, max_age_seconds: int) -> int:
        """清理舊記錄"""
        raise NotImplementedError

    def cleanup_specific_record(self, task_id: str) -> bool:
        """清理特定任務的記錄"""
        raise NotImplementedError


class RedisRetryStorage(RetryStorage):
    """Redis 重試存儲"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self.key_prefix = "dramatiq_retry_tracker:"
        logger.info("🔧 Redis 重試存儲初始化完成")
    
    def store_retry_info(self, task_id: str, retry_info: Dict[str, Any]) -> None:
        """存儲重試信息到 Redis"""
        key = f"{self.key_prefix}{task_id}"
        value = json.dumps(retry_info)
        
        # 設置過期時間為1小時
        self.redis.setex(key, 3600, value)
        logger.debug(f"[REDIS_STORAGE] 存儲重試信息: {key}")
    
    def get_retry_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """從 Redis 獲取重試信息"""
        key = f"{self.key_prefix}{task_id}"
        value = self.redis.get(key)
        
        if value:
            try:
                retry_info = json.loads(value.decode('utf-8'))
                logger.debug(f"[REDIS_STORAGE] 獲取重試信息: {key}")
                return retry_info
            except (json.JSONDecodeError, AttributeError) as e:
                logger.error(f"[REDIS_STORAGE] 解析重試信息失敗: {e}")
                return None
        
        return None
    
    def cleanup_old_records(self, max_age_seconds: int) -> int:
        """清理 Redis 中的舊記錄"""
        # Redis 記錄有自動過期，不需要手動清理
        logger.debug("[REDIS_STORAGE] Redis 記錄自動過期，跳過手動清理")
        return 0

    def cleanup_specific_record(self, task_id: str) -> bool:
        """清理 Redis 中的特定記錄"""
        key = f"{self.key_prefix}{task_id}"
        result = self.redis.delete(key)
        if result > 0:
            logger.info(f"[REDIS_STORAGE] 清理特定記錄: {key}")
            return True
        return False


class MemoryRetryStorage(RetryStorage):
    """內存重試存儲"""
    
    def __init__(self):
        self.storage: Dict[str, Dict[str, Any]] = {}
        logger.info("🔧 內存重試存儲初始化完成")
    
    def store_retry_info(self, task_id: str, retry_info: Dict[str, Any]) -> None:
        """存儲重試信息到內存"""
        self.storage[task_id] = retry_info
        logger.debug(f"[MEMORY_STORAGE] 存儲重試信息: {task_id}")
    
    def get_retry_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """從內存獲取重試信息"""
        retry_info = self.storage.get(task_id)
        if retry_info:
            logger.debug(f"[MEMORY_STORAGE] 獲取重試信息: {task_id}")
        return retry_info
    
    def cleanup_old_records(self, max_age_seconds: int) -> int:
        """清理內存中的舊記錄"""
        current_time = time.time()
        old_keys = []
        
        for task_id, retry_info in self.storage.items():
            last_updated = retry_info.get('last_updated', 0)
            if current_time - last_updated > max_age_seconds:
                old_keys.append(task_id)
        
        for key in old_keys:
            del self.storage[key]
        
        if old_keys:
            logger.info(f"[MEMORY_STORAGE] 清理 {len(old_keys)} 個舊記錄")
        
        return len(old_keys)

    def cleanup_specific_record(self, task_id: str) -> bool:
        """清理內存中的特定記錄"""
        if task_id in self.storage:
            del self.storage[task_id]
            logger.info(f"[MEMORY_STORAGE] 清理特定記錄: {task_id}")
            return True
        return False


# 全局重試追蹤器實例
_retry_tracker: Optional[DramatiqRetryTracker] = None


def get_retry_tracker() -> DramatiqRetryTracker:
    """獲取全局重試追蹤器實例"""
    global _retry_tracker
    if _retry_tracker is None:
        _retry_tracker = DramatiqRetryTracker()
    return _retry_tracker


def track_task_failure(task_id: str, vendor_code: str, mo: str,
                      exception: Exception, max_retries: int = 3) -> bool:
    """
    追蹤任務失敗並決定是否發送通知
    使用 Dramatiq 作為重試狀態的權威來源，解決計數同步問題

    Args:
        task_id: 任務ID
        vendor_code: 廠商代碼
        mo: MO編號
        exception: 異常
        max_retries: 最大重試次數

    Returns:
        bool: 是否應該發送通知
    """
    tracker = get_retry_tracker()

    # 不可重試的異常類型
    non_retriable = (ValueError, FileNotFoundError)
    is_retriable = not isinstance(exception, non_retriable)

    if not is_retriable:
        logger.info(f"[RETRY_TRACKER] 不可重試異常，立即發送通知: {type(exception).__name__}")
        tracker.cleanup_specific_record(task_id)
        return True

    # 🔥 核心修復：使用混合策略 - 優先 Dramatiq，回退到自定義追蹤
    dramatiq_retry_count = _get_dramatiq_retry_count()

    # 獲取自定義追蹤器的計數作為回退
    custom_retries, has_custom_record = tracker.get_retry_count(task_id)

    # 決定使用哪個計數作為權威來源
    if dramatiq_retry_count > 0:
        # 如果 Dramatiq 有重試信息，使用 Dramatiq
        authoritative_retry_count = dramatiq_retry_count
        source = "dramatiq"
    else:
        # 否則使用自定義追蹤器並增加計數
        authoritative_retry_count = tracker.increment_retry_count(task_id) - 1  # -1 因為 increment 已經加了1
        source = "custom_tracker"

    # 同步更新自定義追蹤器狀態（如果來源不是自定義追蹤器）
    if source == "dramatiq":
        tracker.track_task_attempt(
            task_id=task_id,
            vendor_code=vendor_code,
            mo=mo,
            attempt_number=authoritative_retry_count + 1,  # +1 因為這是當前失敗的嘗試
            is_retry=authoritative_retry_count > 0
        )

    # 🔧 修復重試邏輯：檢查是否已達到最大重試次數
    # authoritative_retry_count 是當前失敗前的重試次數，+1 是包含這次失敗的總嘗試次數
    total_attempts = authoritative_retry_count + 1
    should_notify = total_attempts >= max_retries

    # 🔧 額外檢查：如果Dramatiq顯示"Retries exceeded"，強制發送通知
    if not should_notify:
        # 檢查是否是最後一次重試失敗
        try:
            from dramatiq.middleware.current_message import get_current_message
            current_message = get_current_message()
            if current_message and hasattr(current_message, 'options'):
                retries_left = current_message.options.get('retries', 0)
                if retries_left <= 0:
                    logger.info(f"[RETRY_TRACKER] Dramatiq重試已耗盡，強制發送通知: {vendor_code}/{mo}")
                    should_notify = True
        except (ImportError, AttributeError):
            # 如果無法獲取當前消息，檢查是否已達到最大重試次數
            if total_attempts >= max_retries:
                logger.info(f"[RETRY_TRACKER] 達到最大重試次數，強制發送通知: {vendor_code}/{mo}")
                should_notify = True

    logger.info(
        f"[RETRY_TRACKER] 任務失敗追蹤 (修復版): {vendor_code}/{mo} - "
        f"source={source}, retries={authoritative_retry_count}, attempt={total_attempts}/{max_retries}, "
        f"notify={should_notify}"
    )

    # 如果達到最大嘗試次數，清理記錄
    if should_notify:
        logger.info(f"[RETRY_TRACKER] 達到最大重試限制，清理任務記錄: {task_id}")
        tracker.cleanup_specific_record(task_id)

    return should_notify


def _get_dramatiq_retry_count() -> int:
    """
    從 Dramatiq 獲取當前任務的權威重試次數
    這是修復同步問題的核心函數
    
    Returns:
        int: 當前重試次數 (0-based)
    """
    try:
        # 嘗試從 dramatiq 的不同 API 獲取當前消息
        current_message = None
        
        # 方法 1: 嘗試 dramatiq.middleware.current_message
        try:
            from dramatiq.middleware.current_message import get_current_message
            current_message = get_current_message()
        except (ImportError, AttributeError):
            pass
            
        # 方法 2: 嘗試從 dramatiq 直接導入
        if not current_message:
            try:
                import dramatiq
                if hasattr(dramatiq, 'get_current_message'):
                    current_message = dramatiq.get_current_message()
            except (ImportError, AttributeError):
                pass
        
        # 方法 3: 嘗試從 threading local 獲取
        if not current_message:
            try:
                import threading
                if hasattr(threading.current_thread(), 'dramatiq_message'):
                    current_message = threading.current_thread().dramatiq_message
            except AttributeError:
                pass
        
        # 如果無法獲取當前消息，返回 0（非 Dramatiq 環境或首次執行）
        if not current_message:
            logger.debug("[RETRY_TRACKER] 非 Dramatiq 執行環境或首次執行，重試次數為 0")
            return 0
        
        # 🔧 修復：從消息中提取重試次數，正確處理Dramatiq的重試邏輯
        retry_count = 0
        max_retries = 3  # 預設值
        retries_left = 0

        # 檢查 options 中的重試信息
        if hasattr(current_message, 'options') and current_message.options:
            options = current_message.options
            max_retries = options.get('max_retries', 3)
            retries_left = options.get('retries', max_retries)

            # 🔧 關鍵修復：計算當前重試次數
            # Dramatiq邏輯：retries是剩餘重試次數，max_retries是總重試次數
            # 當前重試次數 = max_retries - retries_left
            retry_count = max_retries - retries_left

            logger.debug(f"[RETRY_TRACKER] Dramatiq重試狀態: max_retries={max_retries}, retries_left={retries_left}, current_retry={retry_count}")

            # 🔧 特殊處理：如果retries_left為0，表示重試已耗盡
            if retries_left <= 0 and retry_count >= max_retries:
                logger.info(f"[RETRY_TRACKER] 檢測到Dramatiq重試已耗盡: retry_count={retry_count}, max_retries={max_retries}")
                return max_retries  # 返回最大重試次數表示已達上限

            if retry_count > 0:
                return retry_count

        # 檢查 metadata 中的 retries (備用方法)
        if hasattr(current_message, 'metadata') and current_message.metadata:
            retry_count = current_message.metadata.get('retries', 0)
            if retry_count > 0:
                logger.debug(f"[RETRY_TRACKER] 從 message.metadata 獲取重試次數: {retry_count}")
                return retry_count

        # 檢查消息直接屬性 (備用方法)
        for attr in ['retries', 'retry_count', 'attempts']:
            if hasattr(current_message, attr):
                attr_value = getattr(current_message, attr, 0)
                if attr_value > 0:
                    logger.debug(f"[RETRY_TRACKER] 從 message.{attr} 獲取重試次數: {attr_value}")
                    return attr_value

        logger.debug(f"[RETRY_TRACKER] 當前消息無重試信息，默認為首次執行 (retry_count=0)")
        return 0
        
    except Exception as e:
        logger.debug(f"[RETRY_TRACKER] 獲取 Dramatiq 重試次數時出錯: {e}，默認為 0")
        return 0


def send_failure_notification_if_needed(
    task_id: str, vendor_code: str, mo: str, temp_path: str,
    pd: str, lot: str, error_message: str, exception: Exception,
    email_subject: str = "", email_body: str = "",
    tracking_id: str = "", processing_time: float = 0.0,
    retry_count: int = 0
) -> bool:
    """
    在需要時發送失敗通知
    
    Returns:
        bool: 是否發送了通知
    """
    should_notify = track_task_failure(task_id, vendor_code, mo, exception)
    
    if should_notify:
        tracker = get_retry_tracker()
        retry_count, _ = tracker.get_retry_count(task_id)
        
        logger.info(f"[RETRY_TRACKER] 🔔 應發送失敗通知: {vendor_code}/{mo}")
        
        # 強制嘗試發送實際通知，不使用模擬模式
        notification_service = tracker.notification_service
        
        # 如果之前初始化失敗，現在再次嘗試
        if not notification_service:
            logger.info(f"[RETRY_TRACKER] 🔧 重新嘗試初始化通知服務: {vendor_code}/{mo}")
            try:
                from dotenv import load_dotenv
                load_dotenv()
                
                from src.services.vendor_file_notification import get_vendor_file_notification_service
                notification_service = get_vendor_file_notification_service()
                tracker.notification_service = notification_service
                logger.info("✅ 通知服務重新初始化成功")
            except Exception as init_error:
                logger.error(f"❌ 通知服務重新初始化失敗: {init_error}")
                return False
        
        try:
            logger.info(f"[RETRY_TRACKER] 🚀 發送實際 LINE 通知: {vendor_code}/{mo}")
            success = notification_service.notify_vendor_file_processing_failure(
                vendor_code=vendor_code,
                mo=mo,
                temp_path=temp_path,
                pd=pd,
                lot=lot,
                error_message=error_message,
                email_subject=email_subject,
                email_body=email_body,
                task_id=task_id,
                tracking_id=tracking_id,
                processing_time=processing_time,
                retry_count=retry_count
            )
            
            if success:
                logger.info(f"[RETRY_TRACKER] ✅ 通知發送成功: {vendor_code}/{mo}")
            else:
                logger.error(f"[RETRY_TRACKER] ❌ 通知發送失敗: {vendor_code}/{mo}")
            
            return success
            
        except Exception as e:
            logger.error(f"[RETRY_TRACKER] ❌ 發送通知時出錯: {e}")
            return False
    
    else:
        logger.info(f"[RETRY_TRACKER] ⏳ 任務可重試，暫不發送通知: {vendor_code}/{mo}")
        return False