/* 
 * 布局樣式模組
 * 包含應用程式主要布局、導航列、側邊欄等結構樣式
 */

/* ==================== 應用程式主要布局 ==================== */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-light);
}

/* ==================== 導航列樣式 ==================== */
.navbar {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    height: var(--header-height);
}

.navbar-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
    text-decoration: none;
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.5em;
}

.brand-text {
    font-weight: 700;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--secondary-color);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-link i {
    font-size: 1.1em;
}

/* 下拉選單 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    padding: var(--spacing-xxs) 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: var(--z-dropdown);
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.dropdown-header {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-muted);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-xxs);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: var(--spacing-xxs) 0;
}

/* 導航列工具區 */
.navbar-tools {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    background-color: var(--bg-light);
    font-size: var(--font-size-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
}

.status-indicator.status-normal {
    background-color: var(--success-color);
}

.status-indicator.status-warning {
    background-color: var(--warning-color);
}

.status-indicator.status-error {
    background-color: var(--error-color);
}

/* 通知中心 */
.notification-toggle {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.notification-dropdown {
    right: 0;
    left: auto;
    width: 300px;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.no-notifications {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-muted);
}

.no-notifications i {
    font-size: 2em;
    margin-bottom: var(--spacing-sm);
    opacity: 0.5;
}

/* 行動版選單切換 */
.navbar-toggle {
    display: none;
}

.navbar-toggler {
    background: none;
    border: none;
    padding: var(--spacing-xxs);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.toggler-icon {
    width: 20px;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.navbar-toggler.active .toggler-icon:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggler.active .toggler-icon:nth-child(2) {
    opacity: 0;
}

.navbar-toggler.active .toggler-icon:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ==================== 側邊欄樣式 ==================== */
.sidebar {
    width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    z-index: var(--z-fixed);
    transition: var(--transition);
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--secondary-color);
}

.sidebar.collapsed .brand-text {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xxs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.sidebar-section {
    margin-bottom: var(--spacing-lg);
}

.sidebar-section-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar.collapsed .sidebar-section-title {
    display: none;
}

/* 快速操作區 */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
}

.sidebar.collapsed .quick-actions {
    grid-template-columns: 1fr;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xxs);
    padding: var(--spacing-sm);
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--secondary-color);
    transition: var(--transition);
    font-size: var(--font-size-xs);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.2em;
}

.sidebar.collapsed .quick-action-btn span {
    display: none;
}

/* 系統統計區 */
.system-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--bg-light);
    border-radius: var(--radius-sm);
}

.stat-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

.stat-info {
    flex: 1;
}

.sidebar.collapsed .stat-info {
    display: none;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: 2px;
}

.stat-value {
    font-weight: 600;
    color: var(--secondary-color);
}

/* 最近活動區 */
.recent-activities {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.activity-item:hover {
    background: var(--bg-light);
}

.activity-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--info-color);
    color: white;
    border-radius: 50%;
    font-size: var(--font-size-xs);
    flex-shrink: 0;
}

.activity-info {
    flex: 1;
}

.sidebar.collapsed .activity-info {
    display: none;
}

.activity-text {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-bottom: 2px;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* 外部連結區 */
.external-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxs);
}

.external-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    color: var(--secondary-color);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.external-link:hover {
    background: var(--primary-color);
    color: white;
}

.external-link i {
    width: 16px;
    text-align: center;
}

.sidebar.collapsed .external-link span {
    display: none;
}

/* 側邊欄底部 */
.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxs);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.sidebar.collapsed .info-item span {
    display: none;
}

/* ==================== 主要內容區域 ==================== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: var(--spacing-lg);
    transition: var(--transition);
    min-height: calc(100vh - var(--header-height));
}

.main-content.full-width {
    margin-left: 0;
}

.main-content.sidebar-collapsed {
    margin-left: 60px;
}

/* 頁面標題區 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-content {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
}

/* ==================== 響應式設計 ==================== */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .navbar-toggle {
        display: block;
    }
    
    .navbar-nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        align-items: stretch;
        padding: var(--spacing-md);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: var(--transition);
    }
    
    .navbar-nav.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .nav-item {
        width: 100%;
    }
    
    .nav-link {
        justify-content: flex-start;
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        margin-left: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .main-content {
        padding: var(--spacing-md);
    }
    
    .page-content {
        padding: var(--spacing-md);
    }
}

/* ==================== 載入和模態框狀態 ==================== */
body.modal-open,
body.dialog-open {
    overflow: hidden;
}

body.loading-active {
    overflow: hidden;
}

/* ==================== 無障礙性支援 ==================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦點樣式 */
.nav-link:focus,
.dropdown-item:focus,
.quick-action-btn:focus,
.external-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
    .navbar,
    .sidebar,
    .page-content {
        border-width: 2px;
    }
    
    .nav-link:hover,
    .nav-link.active {
        outline: 2px solid currentColor;
    }
}