# Dashboard Route Integration Requirements

## Introduction

目前系統中存在兩個不同的儀表板頁面：
- `http://localhost:5555/dashboard` (無斜線) - 網路瀏覽器即時儀表板
- `http://localhost:5555/dashboard/` (有斜線) - 統一監控儀表板

需要整合這兩個路由，提供統一的用戶體驗，避免功能重複和用戶混淆。

## Requirements

### Requirement 1: Route Unification

**User Story:** As a system administrator, I want to access all monitoring functionality through the unified dashboard URL, so that I have a single comprehensive monitoring interface.

#### Acceptance Criteria

1. WHEN I access `http://localhost:5555/dashboard` THEN the system SHALL redirect me to `http://localhost:5555/dashboard/`
2. WHEN I access `http://localhost:5555/dashboard/` THEN the system SHALL display the unified monitoring dashboard with all monitoring features including network monitoring
3. WHEN I access the unified dashboard THEN the system SHALL provide comprehensive monitoring experience with all integrated features

### Requirement 2: Feature Integration

**User Story:** As a system administrator, I want all monitoring features consolidated in one dashboard, so that I can monitor all system aspects from a single interface.

#### Acceptance Criteria

1. WHEN I view the unified dashboard THEN the system SHALL display network monitoring features from the original network browser dashboard
2. WHEN I view the unified dashboard THEN the system SHALL display all existing monitoring blocks (email, Dramatiq, system, business, pipeline, vendor files)
3. WHEN I view the unified dashboard THEN the system SHALL include network monitoring as an additional monitoring block

### Requirement 3: Network Monitoring Integration

**User Story:** As a system administrator, I want network monitoring features integrated into the unified dashboard, so that I can monitor network status alongside other system metrics.

#### Acceptance Criteria

1. WHEN I view the unified dashboard THEN the system SHALL display staging task statistics (pending, processing, completed, failed)
2. WHEN I view the unified dashboard THEN the system SHALL display processing task statistics (active, queued, completed, errors)
3. WHEN I view the unified dashboard THEN the system SHALL display network status information (connection status, latency, target host)
4. WHEN network monitoring data updates THEN the system SHALL reflect changes in real-time via WebSocket

### Requirement 4: Backward Compatibility

**User Story:** As a system user, I want existing bookmarks and links to continue working, so that I don't experience broken functionality.

#### Acceptance Criteria

1. WHEN I access the old network dashboard URL THEN the system SHALL either redirect to the unified dashboard or provide a clear migration path
2. WHEN I use existing API endpoints THEN the system SHALL continue to function without breaking changes
3. WHEN I access the unified dashboard THEN the system SHALL maintain all existing functionality

### Requirement 5: User Experience Consistency

**User Story:** As a system administrator, I want a consistent visual and functional experience across all monitoring features, so that I can efficiently navigate and use the dashboard.

#### Acceptance Criteria

1. WHEN I view the unified dashboard THEN the system SHALL use consistent visual styling for all monitoring blocks
2. WHEN I interact with monitoring features THEN the system SHALL provide consistent navigation and interaction patterns
3. WHEN I refresh or reload the dashboard THEN the system SHALL maintain the same layout and functionality
4. WHEN I view monitoring data THEN the system SHALL use consistent data formatting and presentation

### Requirement 6: WebSocket 即時通信

**User Story:** As a system administrator, I want real-time updates for all monitoring data through WebSocket connections, so that I can monitor system changes immediately without manual refresh.

#### Acceptance Criteria

1. WHEN I connect to the dashboard THEN the system SHALL establish a WebSocket connection for real-time updates
2. WHEN I subscribe to specific metric types THEN the system SHALL only send updates for those subscribed types
3. WHEN monitoring data changes THEN the system SHALL push updates via WebSocket within 5 seconds
4. WHEN WebSocket connection fails THEN the system SHALL provide fallback mechanisms and reconnection attempts

#### 支援的訂閱類型

| 訂閱類型 | 說明 | 數據內容 |
|---------|------|----------|
| `metrics_update` | 所有指標更新 | 完整的監控指標數據 |
| `alert` | 系統告警 | 告警級別、訊息、時間戳 |
| `system_status` | 系統狀態變化 | 服務狀態、健康檢查結果 |
| `email_metrics` | 郵件處理指標 | 待處理、處理中、已完成、失敗數量 |
| `dramatiq_metrics` | Dramatiq 任務指標 | 各任務類型的執行統計 |
| `system_metrics` | 系統資源指標 | CPU、記憶體、磁碟使用率 |
| `file_metrics` | 檔案處理指標 | 檔案處理速度、成功率 |
| `business_metrics` | 業務指標 | MO、LOT、品質分數等業務數據 |
| `network_metrics` | 網路監控指標 | 網路連接狀態、延遲等 |

#### WebSocket 訊息格式

```json
{
  "type": "subscription_request",
  "payload": {
    "types": ["metrics_update", "alert", "system_status"]
  }
}
```

回應格式：
```json
{
  "type": "connection_info",
  "payload": {
    "action": "subscribed",
    "subscriptions": ["metrics_update", "alert", "system_status"],
    "invalid_subscriptions": [],
    "total_subscriptions": 3
  },
  "client_id": "dashboard_xxxxx_timestamp"
}
```

### Requirement 7: Performance Optimization

**User Story:** As a system administrator, I want the unified dashboard to load quickly and update efficiently, so that I can monitor systems without performance delays.

#### Acceptance Criteria

1. WHEN I access the unified dashboard THEN the system SHALL load the initial page within 2 seconds
2. WHEN monitoring data updates THEN the system SHALL push updates via WebSocket within 5 seconds of data changes
3. WHEN multiple monitoring blocks are displayed THEN the system SHALL collect data in parallel to minimize loading time
4. WHEN I navigate between dashboard sections THEN the system SHALL maintain responsive performance