# File Management Module - 檔案管理功能模組

## 概述

檔案管理功能模組負責處理系統中的所有檔案相關操作，包括檔案上傳、檔案瀏覽、附件管理和檔案處理功能。

## 功能特性

### 核心功能
- **檔案管理器** - 瀏覽和管理系統中的檔案
- **檔案上傳** - 支援多種格式的檔案上傳
- **附件瀏覽器** - 查看和管理郵件附件
- **檔案處理** - 自動處理和轉換檔案格式

### 支援格式
- **CSV 檔案** - 測試數據檔案
- **Excel 檔案** - .xlsx, .xls 格式
- **壓縮檔案** - .zip, .7z, .rar 格式
- **文字檔案** - .txt, .log 格式
- **圖片檔案** - .png, .jpg, .gif 格式

## 目錄結構

```
file-management/
├── templates/               # HTML 模板
│   ├── file_manager.html    # 檔案管理器
│   ├── upload.html          # 檔案上傳
│   └── attachment_browser.html # 附件瀏覽器
├── static/                  # 靜態資源
│   ├── css/
│   │   └── file-manager.css # 檔案管理樣式
│   ├── js/
│   │   ├── file-upload.js   # 檔案上傳邏輯
│   │   ├── file-browser.js  # 檔案瀏覽邏輯
│   │   └── file-api.js      # 檔案 API
│   └── images/              # 檔案類型圖示
├── components/              # 可重用組件
│   ├── file-uploader.html   # 上傳組件
│   └── file-list.html       # 檔案列表組件
├── routes/                  # 路由處理
│   └── file_routes.py       # 檔案路由
└── README.md                # 本檔案
```

## API 端點

### 檔案管理
- `GET /files/manager` - 檔案管理器頁面
- `GET /files/upload` - 檔案上傳頁面
- `GET /api/files/list` - 獲取檔案列表
- `GET /api/files/<id>` - 獲取檔案詳情
- `DELETE /api/files/<id>` - 刪除檔案

### 檔案上傳
- `POST /api/files/upload` - 上傳檔案
- `POST /api/files/upload/chunk` - 分塊上傳
- `GET /api/files/upload/status/<job_id>` - 獲取上傳狀態

### 檔案下載
- `GET /api/files/<id>/download` - 下載檔案
- `GET /api/files/<id>/preview` - 預覽檔案
- `GET /api/files/<id>/thumbnail` - 獲取縮圖

### 檔案處理
- `POST /api/files/<id>/process` - 處理檔案
- `GET /api/files/processing/status/<job_id>` - 獲取處理狀態
- `GET /api/files/processing/results/<job_id>` - 獲取處理結果

### 附件管理
- `GET /files/attachments` - 附件瀏覽器頁面
- `GET /api/files/attachments` - 獲取附件列表
- `GET /api/files/attachments/<id>/extract` - 解壓縮附件

## 資料模型

### File
- `id`: 檔案唯一識別碼
- `filename`: 檔案名稱
- `original_filename`: 原始檔案名稱
- `file_path`: 檔案路徑
- `file_size`: 檔案大小
- `file_type`: 檔案類型
- `mime_type`: MIME 類型
- `checksum`: 檔案校驗碼
- `upload_date`: 上傳日期
- `status`: 檔案狀態 (uploaded, processing, processed, error)

### FileProcessingJob
- `id`: 處理任務識別碼
- `file_id`: 檔案識別碼
- `job_type`: 處理類型 (extract, convert, analyze)
- `status`: 處理狀態 (pending, running, completed, failed)
- `progress`: 處理進度 (0-100)
- `result_data`: 處理結果
- `error_message`: 錯誤訊息
- `created_at`: 建立時間
- `completed_at`: 完成時間

### Attachment
- `id`: 附件唯一識別碼
- `email_id`: 關聯郵件識別碼
- `filename`: 檔案名稱
- `file_size`: 檔案大小
- `content_type`: 內容類型
- `is_extracted`: 是否已解壓縮
- `extraction_path`: 解壓縮路徑
- `vendor_type`: 廠商類型

## 檔案處理功能

### 自動處理
- **格式檢測** - 自動識別檔案格式和編碼
- **數據提取** - 從 CSV/Excel 檔案提取關鍵數據
- **壓縮檔解壓** - 自動解壓縮附件檔案
- **格式轉換** - CSV 轉 Excel，數據格式標準化

### 廠商特定處理
- **ETD 檔案** - 處理 `anf` 關鍵字相關檔案
- **GTK 檔案** - 處理 `ft hold`, `ft lot` 相關檔案
- **JCET 檔案** - 處理 `jcet` 相關檔案
- **LINGSEN 檔案** - 處理 `lingsen` 相關檔案
- **XAHT 檔案** - 處理 `tianshui`, `西安` 相關檔案

## 安全考量

### 檔案驗證
- 檔案類型白名單驗證
- 檔案大小限制 (預設 100MB)
- 病毒掃描整合
- 檔案內容安全檢查

### 存取控制
- 使用者權限驗證
- 檔案存取日誌記錄
- 敏感檔案加密存儲
- 定期檔案清理機制

## 開發注意事項

- 支援大檔案分塊上傳
- 實作檔案上傳進度顯示
- 提供檔案預覽功能
- 確保跨平台路徑相容性 (Windows/Linux)
- 實作適當的錯誤處理和重試機制
- 支援批次檔案操作