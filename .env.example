# POP3 郵件伺服器設定
POP3_SERVER=pop.gmail.com
POP3_PORT=995
POP3_USERNAME=<EMAIL>
POP3_PASSWORD=your_app_password
POP3_USE_SSL=True

# 資料庫設定
DB_PATH=./emails.db

# 日誌設定
LOG_LEVEL=INFO
LOG_FILE=./logs/email_inbox.log

# 統一 LLM 配置
# LLM 提供者選擇: ollama 或 grok
LLM_PROVIDER=ollama

# Ollama 配置 (當 LLM_PROVIDER=ollama 時使用)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3:latest
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3

# Grok 配置 (當 LLM_PROVIDER=grok 時使用)
GROK_API_KEY=your_grok_api_key_here
GROK_MODEL=grok-beta
GROK_BASE_URL=https://api.x.ai/v1

# 通用 LLM 配置
LLM_PARSING_ENABLED=true
LLM_PARSING_MODE=fallback
LLM_CONFIDENCE_THRESHOLD=0.7

# AI 模型配置
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EXPECTED_VECTOR_DIMENSION=384

# Flask 前端應用程式配置
FLASK_APP=frontend.app:create_app
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Flask 安全配置
SECRET_KEY=your_secret_key_here_change_in_production

# 檔案上傳配置
UPLOAD_FOLDER=temp/uploads
MAX_CONTENT_LENGTH=16777216

# 靜態資源配置
SEND_FILE_MAX_AGE_DEFAULT=0
TEMPLATES_AUTO_RELOAD=True

# 快取配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# API 配置
API_TIMEOUT=30
API_RETRY_COUNT=3

# WebSocket 配置
WEBSOCKET_HOST=127.0.0.1
WEBSOCKET_PORT=8765

# Redis 配置 (用於任務隊列)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 後端服務配置
FLASK_EMAIL_SERVICE_HOST=127.0.0.1
FLASK_EMAIL_SERVICE_PORT=5000
FASTAPI_EQC_SERVICE_HOST=127.0.0.1
FASTAPI_EQC_SERVICE_PORT=8010

# LINE 通知配置
LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token
LINE_USER_ID=your_line_user_id