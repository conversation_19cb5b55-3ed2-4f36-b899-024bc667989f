# 真實數據源整合指南

## 概述

本文檔說明統一監控儀表板如何整合真實的系統數據源，提供準確的監控信息，而不是依賴模擬數據。

## 整合的數據源

### 1. 系統資源監控

#### 真實數據來源
- **CPU 使用率**: 使用 `psutil.cpu_percent()` 獲取實時 CPU 使用率
- **記憶體使用率**: 使用 `psutil.virtual_memory()` 獲取記憶體統計
- **磁碟使用率**: 使用 `psutil.disk_usage()` 獲取磁碟空間信息
- **網路連接**: 使用 `psutil.net_connections()` 獲取活躍連接數

#### 實作位置
- `src/dashboard_monitoring/collectors/dashboard_system_collector.py`
- `src/dashboard_monitoring/api/real_data_api.py`

#### 容錯機制
```python
try:
    import psutil
    cpu_percent = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
except ImportError:
    logger.warning("psutil 不可用，使用模擬數據")
    # 回退到合理的模擬數據
```

### 2. 服務健康狀態監控

#### 支援的服務檢測

##### Redis 服務
- **檢測方式**: 嘗試連接多個常見配置
- **配置**: localhost:6379, 127.0.0.1:6379, localhost:6380
- **超時設置**: 2秒連接超時
- **狀態**: healthy, warning, error, unknown

```python
async def _check_redis_connection(self) -> ServiceHealth:
    redis_configs = [
        {'host': 'localhost', 'port': 6379, 'db': 0},
        {'host': '127.0.0.1', 'port': 6379, 'db': 0},
        {'host': 'localhost', 'port': 6380, 'db': 0},
    ]
    
    for config in redis_configs:
        try:
            r = redis.Redis(socket_timeout=2, socket_connect_timeout=2, **config)
            r.ping()
            return ServiceHealth.HEALTHY
        except Exception:
            continue
    
    return ServiceHealth.ERROR
```

##### 資料庫服務
- **檢測方式**: 掃描常見的資料庫文件路徑
- **支援格式**: SQLite (.db, .sqlite, .sqlite3)
- **檢測路徑**: outlook.db, email_inbox.db, data/email_inbox.db
- **驗證方式**: 嘗試連接並執行簡單查詢

##### Dramatiq 服務
- **檢測方式**: 掃描系統進程，查找 dramatiq 關鍵字
- **依賴**: Redis 服務必須可用
- **進程檢測**: 使用 `psutil.process_iter()` 掃描命令行參數

### 3. 廠商文件統計

#### 數據源檢測器 (`data_source_detector.py`)

##### 自動檢測功能
- **廠商目錄掃描**: 自動掃描 `doc/`, `attachments/`, `temp/`, `data/` 目錄
- **文件統計**: 計算文件數量、總大小、最近活動時間
- **廠商識別**: 基於目錄名稱自動識別廠商類型

##### 支援的廠商
```python
vendor_names = [
    'GTK', 'ETD', 'JCET', 'LINGSEN', 'XAHT', 
    'MSEC', 'NANOTECH', 'NFME', 'SUQIAN', 'TSHT', 'CHUZHOU'
]
```

##### 統計計算
- **成功率**: 基於文件數量和最近活動計算
- **效能分數**: 綜合文件數量、活動頻率、文件大小
- **最後活動**: 基於文件修改時間計算

#### 真實文件系統掃描
```python
async def _get_vendor_stats_from_filesystem(self, vendor: str):
    vendor_dirs = [
        f"doc/{vendor}*",
        f"temp/{vendor}*", 
        f"data/{vendor}*",
        f"attachments/*{vendor}*"
    ]
    
    for pattern in vendor_dirs:
        paths = glob.glob(pattern, recursive=True)
        for path in paths:
            if os.path.isfile(path):
                # 統計文件信息
                total_files += 1
                file_size = os.path.getsize(path)
                total_size += file_size
```

### 4. 資料庫信息監控

#### 自動資料庫發現
- **掃描模式**: `*.db`, `*.sqlite`, `*.sqlite3`, `data/*.db`
- **分析內容**: 表數量、記錄數、文件大小、最後修改時間
- **連接測試**: 驗證資料庫可訪問性

#### 資料庫指標
```python
{
    'total_databases': 3,
    'accessible_databases': 2,
    'total_size_mb': 45.67,
    'total_tables': 15,
    'total_records': 12450
}
```

## API 端點

### 真實數據 API (`/dashboard/api/real/`)

#### 可用端點
- `GET /dashboard/api/real/system` - 系統指標
- `GET /dashboard/api/real/vendors` - 廠商統計
- `GET /dashboard/api/real/database` - 資料庫信息
- `GET /dashboard/api/real/all` - 所有真實數據

#### 回應格式
```json
{
    "status": "success",
    "data": {
        "system_metrics": {...},
        "service_health": {...},
        "vendor_statistics": {...},
        "database_info": {...}
    },
    "timestamp": "2025-08-08T12:00:00.000Z"
}
```

## 緩存機制

### 智能緩存策略
- **緩存時間**: 30秒（可配置）
- **緩存鍵**: 基於數據類型的唯一鍵
- **自動失效**: 基於時間戳的自動失效機制

```python
def _is_cache_valid(self, cache_key: str) -> bool:
    if cache_key not in self.cache or cache_key not in self.last_update:
        return False
    
    cache_age = (datetime.now() - self.last_update[cache_key]).total_seconds()
    return cache_age < self.cache_timeout
```

### 緩存優勢
- **減少系統負載**: 避免頻繁的文件系統掃描
- **提高響應速度**: 快速返回緩存的數據
- **容錯性**: 緩存失效時自動重新收集

## 容錯和回退機制

### 多層回退策略

1. **優先使用真實數據**: 從實際系統服務和文件系統獲取
2. **智能模擬數據**: 基於實際情況生成合理的模擬數據
3. **基礎回退數據**: 提供最基本的默認值

### 錯誤處理
```python
try:
    # 嘗試獲取真實數據
    real_data = await get_real_system_data()
except Exception as e:
    logger.warning(f"獲取真實數據失敗: {e}")
    # 回退到模擬數據
    real_data = generate_simulated_data()
```

## 配置選項

### 環境變數
- `DASHBOARD_CACHE_TIMEOUT`: 緩存超時時間（秒）
- `DASHBOARD_REDIS_HOST`: Redis 主機地址
- `DASHBOARD_REDIS_PORT`: Redis 端口
- `DASHBOARD_DB_PATHS`: 額外的資料庫掃描路徑

### 配置文件
```json
{
    "data_sources": {
        "cache_timeout": 30,
        "redis_configs": [
            {"host": "localhost", "port": 6379},
            {"host": "127.0.0.1", "port": 6379}
        ],
        "vendor_scan_patterns": [
            "doc/*", "attachments/*", "temp/*", "data/*"
        ]
    }
}
```

## 效能考量

### 最佳化策略
- **並行收集**: 多個數據源並行收集
- **智能緩存**: 避免重複的昂貴操作
- **超時控制**: 防止長時間阻塞
- **錯誤隔離**: 單一數據源失敗不影響其他

### 效能指標
- **數據收集時間**: < 2秒
- **API 響應時間**: < 500ms
- **記憶體使用**: < 50MB
- **CPU 影響**: < 5%

## 監控和日誌

### 日誌級別
- **INFO**: 成功的數據收集和服務檢測
- **WARNING**: 回退到模擬數據或服務不可用
- **ERROR**: 嚴重的系統錯誤
- **DEBUG**: 詳細的調試信息

### 監控指標
- 數據源檢測成功率
- 緩存命中率
- API 響應時間
- 錯誤發生頻率

## 故障排除

### 常見問題

#### 1. psutil 不可用
**症狀**: 系統資源顯示為模擬數據
**解決**: 安裝 psutil 套件 `pip install psutil`

#### 2. Redis 連接失敗
**症狀**: Dramatiq 服務狀態顯示為 error
**解決**: 檢查 Redis 服務是否運行，確認端口配置

#### 3. 廠商統計為空
**症狀**: 所有廠商文件數量為 0
**解決**: 檢查廠商目錄是否存在，確認文件權限

#### 4. 資料庫檢測失敗
**症狀**: 資料庫服務狀態為 unknown
**解決**: 確認資料庫文件存在且可讀取

### 調試步驟

1. **檢查日誌**: 查看詳細的錯誤信息
2. **測試 API**: 直接調用 `/dashboard/api/real/all` 端點
3. **驗證依賴**: 確認所需的 Python 套件已安裝
4. **檢查權限**: 確認文件和目錄的讀取權限
5. **網路連接**: 測試 Redis 和其他服務的連接

## 未來擴展

### 計劃中的改進
- **更多數據源**: 支援 PostgreSQL、MySQL 等資料庫
- **自定義掃描**: 允許用戶配置自定義的掃描路徑
- **歷史趨勢**: 記錄和分析歷史數據趨勢
- **告警整合**: 基於真實數據的智能告警

### 擴展指南
1. 在 `DataSourceDetector` 中添加新的檢測方法
2. 在 `RealDataCollector` 中實現數據收集邏輯
3. 更新 API 端點以暴露新的數據
4. 添加相應的測試和文檔