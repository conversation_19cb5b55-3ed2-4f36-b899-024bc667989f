#!/usr/bin/env python3
"""
Dashboard Service Integrator
統一監控儀表板服務整合器

負責將監控儀表板整合到主應用程式中，包括：
- API 路由註冊
- WebSocket 連接管理
- 靜態文件服務
- 健康檢查和狀態監控
"""

import logging
from typing import Optional, Dict, Any
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from pathlib import Path

logger = logging.getLogger(__name__)

class DashboardServiceIntegrator:
    """監控儀表板服務整合器"""
    
    def __init__(self):
        self.app: Optional[FastAPI] = None
        self.is_initialized = False
        self.static_path = Path(__file__).parent.parent / "static"
        self.templates_path = Path(__file__).parent.parent / "templates"
    
    async def initialize(self, app: FastAPI) -> bool:
        """初始化監控儀表板服務"""
        try:
            self.app = app
            logger.info("正在初始化監控儀表板服務...")
            
            # 註冊 API 路由
            self._register_api_routes()
            
            # 註冊 WebSocket 路由
            self._register_websocket_routes()
            
            # 註冊靜態文件服務
            self._register_static_files()
            
            # 啟動監控服務
            await self._start_monitoring_services()
            
            self.is_initialized = True
            logger.info("✅ 監控儀表板服務初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"監控儀表板服務初始化失敗: {e}")
            return False
    
    def _register_api_routes(self):
        """註冊 API 路由"""
        try:
            # 導入並註冊主要 API 路由 (simple_dashboard_api 已經有 /dashboard 前綴)
            from ..api import simple_dashboard_api
            self.app.include_router(simple_dashboard_api.router)
            logger.info("✅ 主要 API 路由已註冊")
            
            # 嘗試導入並註冊其他 API 路由
            try:
                from ..api import dashboard_monitoring_api
                self.app.include_router(dashboard_monitoring_api.router, prefix="/dashboard/api")
                logger.info("✅ 監控 API 路由已註冊")
            except ImportError:
                logger.debug("監控 API 路由不可用，跳過")
            
            try:
                from ..api import dashboard_alert_api
                self.app.include_router(dashboard_alert_api.router, prefix="/dashboard/api")
                logger.info("✅ 警報 API 路由已註冊")
            except ImportError:
                logger.debug("警報 API 路由不可用，跳過")
            
            try:
                from ..api import dashboard_cache_api
                self.app.include_router(dashboard_cache_api.router, prefix="/dashboard/api")
                logger.info("✅ 緩存 API 路由已註冊")
            except ImportError:
                logger.debug("緩存 API 路由不可用，跳過")
            
        except ImportError as e:
            logger.warning(f"主要 API 路由導入失敗: {e}")
        except Exception as e:
            logger.error(f"註冊 API 路由失敗: {e}")
    
    def _register_websocket_routes(self):
        """註冊 WebSocket 路由"""
        try:
            from ..api import dashboard_websocket
            # WebSocket 路由器需要使用正確的前綴
            self.app.include_router(dashboard_websocket.websocket_router, prefix="/dashboard")
            logger.info("✅ WebSocket 路由已註冊")
            
        except ImportError as e:
            logger.warning(f"WebSocket 路由導入失敗: {e}")
        except Exception as e:
            logger.error(f"註冊 WebSocket 路由失敗: {e}")
    
    def _register_static_files(self):
        """註冊靜態文件服務"""
        try:
            if self.static_path.exists():
                self.app.mount(
                    "/dashboard/static", 
                    StaticFiles(directory=str(self.static_path)), 
                    name="dashboard_static"
                )
                logger.info("✅ 靜態文件服務已註冊")
            else:
                logger.warning(f"靜態文件目錄不存在: {self.static_path}")
                
        except Exception as e:
            logger.error(f"註冊靜態文件服務失敗: {e}")
    
    async def _start_monitoring_services(self):
        """啟動監控服務"""
        try:
            # 啟動監控協調器
            from ..core.dashboard_monitoring_coordinator import get_monitoring_coordinator
            coordinator = get_monitoring_coordinator()
            await coordinator.start_monitoring()
            logger.info("✅ 監控協調器已啟動")
            
        except ImportError as e:
            logger.warning(f"監控協調器導入失敗: {e}")
        except Exception as e:
            logger.error(f"啟動監控服務失敗: {e}")
    
    async def get_service_status(self) -> Dict[str, Any]:
        """獲取服務狀態"""
        try:
            if not self.is_initialized:
                return {
                    "service_name": "統一監控儀表板",
                    "status": "not_initialized",
                    "message": "服務未初始化"
                }
            
            # 檢查各個組件狀態
            status = {
                "service_name": "統一監控儀表板",
                "status": "running",
                "components": {
                    "api_routes": "active",
                    "websocket": "active",
                    "static_files": "active" if self.static_path.exists() else "missing",
                    "monitoring": "active"
                },
                "endpoints": [
                    "/dashboard/",
                    "/dashboard/api/metrics/current",
                    "/dashboard/api/alerts/active",
                    "/dashboard/ws"
                ]
            }
            
            return status
            
        except Exception as e:
            logger.error(f"獲取服務狀態失敗: {e}")
            return {
                "service_name": "統一監控儀表板",
                "status": "error",
                "message": str(e)
            }
    
    async def get_health_check(self) -> Dict[str, Any]:
        """健康檢查"""
        try:
            health_status = {
                "overall_status": "healthy",
                "checks": {
                    "service_initialized": self.is_initialized,
                    "static_files_available": self.static_path.exists(),
                    "templates_available": self.templates_path.exists()
                },
                "timestamp": "2025-01-08T12:00:00Z"
            }
            
            # 檢查是否有任何問題
            if not all(health_status["checks"].values()):
                health_status["overall_status"] = "degraded"
            
            return health_status
            
        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")
            return {
                "overall_status": "unhealthy",
                "error": str(e)
            }
    
    async def stop_monitoring(self):
        """停止監控服務"""
        try:
            if self.is_initialized:
                # 停止監控協調器
                from ..core.dashboard_monitoring_coordinator import get_monitoring_coordinator
                coordinator = get_monitoring_coordinator()
                await coordinator.stop_monitoring()
                
                self.is_initialized = False
                logger.info("✅ 監控儀表板服務已停止")
                
        except Exception as e:
            logger.error(f"停止監控服務失敗: {e}")

# 全局實例
_dashboard_integrator: Optional[DashboardServiceIntegrator] = None

def get_dashboard_integrator() -> DashboardServiceIntegrator:
    """獲取監控儀表板整合器實例"""
    global _dashboard_integrator
    if _dashboard_integrator is None:
        _dashboard_integrator = DashboardServiceIntegrator()
    return _dashboard_integrator