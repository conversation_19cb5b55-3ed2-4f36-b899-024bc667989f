<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合規檢查 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('eqc.static', filename='css/eqc.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="compliance-container">
        <header class="compliance-header">
            <h1>合規檢查中心</h1>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-label">合規率:</span>
                    <span class="stat-value {{ 'success' if stats.compliance_rate > 95 else 'warning' if stats.compliance_rate > 80 else 'error' }}">
                        {{ '%.1f'|format(stats.compliance_rate or 0) }}%
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">活躍政策:</span>
                    <span class="stat-value">{{ stats.active_policies or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">違規項目:</span>
                    <span class="stat-value error">{{ stats.violations or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">風險等級:</span>
                    <span class="stat-value {{ stats.risk_level.lower() if stats.risk_level else 'low' }}">
                        {{ stats.risk_level or 'LOW' }}
                    </span>
                </div>
            </div>
            <div class="header-actions">
                <button id="run-compliance-check-btn" class="btn btn-primary">
                    <span class="btn-icon">🔍</span>
                    <span class="btn-text">執行合規檢查</span>
                </button>
                <button id="generate-compliance-report-btn" class="btn btn-secondary">
                    <span class="btn-icon">📋</span>
                    <span class="btn-text">產生合規報告</span>
                </button>
                <a href="{{ url_for('eqc.eqc_dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">回到EQC儀表板</span>
                </a>
            </div>
        </header>

        <div class="compliance-content">
            <!-- 政策管理區 -->
            <div class="policy-management-section">
                <div class="policy-card">
                    <div class="policy-header">
                        <h3>📜 合規政策管理</h3>
                        <div class="policy-actions">
                            <button id="add-policy-btn" class="btn btn-primary">新增政策</button>
                            <button id="import-policies-btn" class="btn btn-secondary">匯入政策</button>
                            <button id="export-policies-btn" class="btn btn-outline">匯出政策</button>
                        </div>
                    </div>

                    <div class="policy-categories">
                        <div class="category-tabs">
                            <button class="tab-btn active" data-category="data-protection">資料保護</button>
                            <button class="tab-btn" data-category="security">資訊安全</button>
                            <button class="tab-btn" data-category="privacy">隱私保護</button>
                            <button class="tab-btn" data-category="industry">行業規範</button>
                            <button class="tab-btn" data-category="internal">內部政策</button>
                        </div>

                        <!-- 資料保護政策 -->
                        <div class="category-content active" id="data-protection-category">
                            <div class="policy-grid">
                                {% for policy in policies['data_protection'] %}
                                <div class="policy-item {{ policy.status }}">
                                    <div class="policy-info">
                                        <h4 class="policy-title">{{ policy.title }}</h4>
                                        <p class="policy-description">{{ policy.description }}</p>
                                        <div class="policy-meta">
                                            <span class="policy-version">v{{ policy.version }}</span>
                                            <span class="policy-updated">更新: {{ policy.updated_at.strftime('%Y-%m-%d') if policy.updated_at else 'N/A' }}</span>
                                            <span class="policy-severity {{ policy.severity.lower() }}">{{ policy.severity }}</span>
                                        </div>
                                    </div>
                                    <div class="policy-controls">
                                        <label class="switch">
                                            <input type="checkbox" {{ 'checked' if policy.enabled else '' }} onchange="togglePolicy('{{ policy.id }}')">
                                            <span class="slider"></span>
                                        </label>
                                        <div class="policy-actions">
                                            <button class="btn btn-sm btn-outline" onclick="editPolicy('{{ policy.id }}')">編輯</button>
                                            <button class="btn btn-sm btn-secondary" onclick="testPolicy('{{ policy.id }}')">測試</button>
                                            <button class="btn btn-sm btn-danger" onclick="deletePolicy('{{ policy.id }}')">刪除</button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- 其他類別的政策內容會類似地定義 -->
                    </div>
                </div>
            </div>

            <!-- 合規儀表板 -->
            <div class="compliance-dashboard-section">
                <div class="dashboard-row">
                    <div class="compliance-chart-card">
                        <h3>合規趨勢</h3>
                        <div class="chart-container">
                            <canvas id="compliance-trend-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="violations-chart-card">
                        <h3>違規分類</h3>
                        <div class="chart-container">
                            <canvas id="violations-category-chart"></canvas>
                        </div>
                    </div>
                    
                    <div class="risk-assessment-card">
                        <h3>風險評估</h3>
                        <div class="risk-meter">
                            <div class="meter-container">
                                <div class="meter-fill" style="width: {{ stats.risk_percentage or 0 }}%"></div>
                                <div class="meter-label">{{ stats.risk_level or 'LOW' }}</div>
                            </div>
                        </div>
                        <div class="risk-factors">
                            {% for factor in risk_factors %}
                            <div class="risk-factor {{ factor.level.lower() }}">
                                <span class="factor-name">{{ factor.name }}</span>
                                <span class="factor-score">{{ factor.score }}/100</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 合規檢查結果 -->
            <div class="compliance-results-section">
                <div class="results-card">
                    <div class="results-header">
                        <h3>🔍 最新檢查結果</h3>
                        <div class="results-filters">
                            <select id="result-timeframe">
                                <option value="today">今天</option>
                                <option value="week" selected>本週</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季</option>
                            </select>
                            <select id="result-severity">
                                <option value="all" selected>全部嚴重性</option>
                                <option value="critical">嚴重</option>
                                <option value="high">高</option>
                                <option value="medium">中等</option>
                                <option value="low">低</option>
                            </select>
                            <select id="result-status">
                                <option value="all" selected>全部狀態</option>
                                <option value="violation">違規</option>
                                <option value="warning">警告</option>
                                <option value="compliant">合規</option>
                            </select>
                            <button id="apply-result-filters-btn" class="btn btn-sm btn-primary">套用篩選</button>
                        </div>
                    </div>

                    <div class="results-summary">
                        <div class="summary-stats">
                            <div class="stat-card critical">
                                <div class="stat-number">{{ summary.critical_violations or 0 }}</div>
                                <div class="stat-label">嚴重違規</div>
                            </div>
                            <div class="stat-card high">
                                <div class="stat-number">{{ summary.high_violations or 0 }}</div>
                                <div class="stat-label">高風險</div>
                            </div>
                            <div class="stat-card medium">
                                <div class="stat-number">{{ summary.medium_violations or 0 }}</div>
                                <div class="stat-label">中等風險</div>
                            </div>
                            <div class="stat-card low">
                                <div class="stat-number">{{ summary.low_violations or 0 }}</div>
                                <div class="stat-label">低風險</div>
                            </div>
                        </div>
                    </div>

                    <div class="results-list" id="compliance-results-list">
                        {% for result in compliance_results %}
                        <div class="result-item {{ result.severity.lower() }} {{ result.status }}">
                            <div class="result-indicator">
                                {% if result.status == 'violation' %}🚨
                                {% elif result.status == 'warning' %}⚠️
                                {% elif result.status == 'compliant' %}✅
                                {% endif %}
                            </div>
                            <div class="result-content">
                                <div class="result-header">
                                    <h4 class="result-title">{{ result.policy_title }}</h4>
                                    <div class="result-meta">
                                        <span class="result-severity {{ result.severity.lower() }}">{{ result.severity }}</span>
                                        <span class="result-category">{{ result.category }}</span>
                                        <span class="result-time">{{ result.checked_at.strftime('%Y-%m-%d %H:%M') if result.checked_at else 'N/A' }}</span>
                                    </div>
                                </div>
                                <div class="result-description">
                                    <p>{{ result.description }}</p>
                                    {% if result.details %}
                                    <div class="result-details">
                                        <strong>詳細資訊:</strong>
                                        <p>{{ result.details }}</p>
                                    </div>
                                    {% endif %}
                                </div>
                                {% if result.affected_items %}
                                <div class="affected-items">
                                    <strong>影響項目:</strong>
                                    <ul>
                                        {% for item in result.affected_items %}
                                        <li>{{ item }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                                {% if result.recommendations %}
                                <div class="recommendations">
                                    <strong>建議措施:</strong>
                                    <ul>
                                        {% for rec in result.recommendations %}
                                        <li>{{ rec }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                            <div class="result-actions">
                                {% if result.status == 'violation' %}
                                <button class="btn btn-sm btn-primary" onclick="createRemediation('{{ result.id }}')">建立修復計畫</button>
                                <button class="btn btn-sm btn-secondary" onclick="assignResponsible('{{ result.id }}')">指派負責人</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline" onclick="viewResultDetails('{{ result.id }}')">詳細資訊</button>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline dropdown-toggle">更多</button>
                                    <div class="dropdown-menu">
                                        <a href="#" onclick="markAsResolved('{{ result.id }}')">標記為已解決</a>
                                        <a href="#" onclick="markAsException('{{ result.id }}')">標記為例外</a>
                                        <a href="#" onclick="exportResult('{{ result.id }}')">匯出結果</a>
                                        <div class="dropdown-divider"></div>
                                        <a href="#" onclick="deleteResult('{{ result.id }}')">刪除結果</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="pagination" id="compliance-results-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>

            <!-- 修復計畫追蹤 -->
            <div class="remediation-tracking-section">
                <div class="tracking-card">
                    <div class="tracking-header">
                        <h3>🔧 修復計畫追蹤</h3>
                        <div class="tracking-actions">
                            <button id="create-remediation-plan-btn" class="btn btn-primary">建立修復計畫</button>
                            <button id="export-remediation-report-btn" class="btn btn-secondary">匯出修復報告</button>
                        </div>
                    </div>

                    <div class="remediation-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ remediation_stats.total_plans or 0 }}</span>
                            <span class="stat-label">總計畫數</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ remediation_stats.in_progress or 0 }}</span>
                            <span class="stat-label">進行中</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ remediation_stats.completed or 0 }}</span>
                            <span class="stat-label">已完成</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ remediation_stats.overdue or 0 }}</span>
                            <span class="stat-label">逾期</span>
                        </div>
                    </div>

                    <div class="remediation-list" id="remediation-plans-list">
                        {% for plan in remediation_plans %}
                        <div class="remediation-item {{ plan.status }} {{ 'overdue' if plan.is_overdue else '' }}">
                            <div class="plan-header">
                                <h4 class="plan-title">{{ plan.title }}</h4>
                                <div class="plan-meta">
                                    <span class="plan-id">#{{ plan.id }}</span>
                                    <span class="plan-priority {{ plan.priority.lower() }}">{{ plan.priority }}</span>
                                    <span class="plan-status {{ plan.status }}">{{ plan.status_display }}</span>
                                    <span class="plan-deadline">截止: {{ plan.deadline.strftime('%Y-%m-%d') if plan.deadline else 'N/A' }}</span>
                                </div>
                            </div>
                            <div class="plan-content">
                                <p class="plan-description">{{ plan.description }}</p>
                                <div class="plan-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: {{ plan.progress_percentage }}%"></div>
                                    </div>
                                    <span class="progress-text">{{ plan.progress_percentage }}% 完成</span>
                                </div>
                                <div class="plan-assignee">
                                    <strong>負責人:</strong> {{ plan.assignee_name or '未指派' }}
                                </div>
                            </div>
                            <div class="plan-actions">
                                <button class="btn btn-sm btn-primary" onclick="updatePlanProgress('{{ plan.id }}')">更新進度</button>
                                <button class="btn btn-sm btn-secondary" onclick="viewPlanDetails('{{ plan.id }}')">檢視詳情</button>
                                <button class="btn btn-sm btn-outline" onclick="editPlan('{{ plan.id }}')">編輯計畫</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 政策編輯模態框 -->
    <div class="modal" id="policy-edit-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="policy-edit-title">編輯合規政策</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="policy-edit-form">
                    <div class="form-group">
                        <label for="policy-title">政策標題:</label>
                        <input type="text" id="policy-title" required>
                    </div>
                    <div class="form-group">
                        <label for="policy-category">政策類別:</label>
                        <select id="policy-category">
                            <option value="data-protection">資料保護</option>
                            <option value="security">資訊安全</option>
                            <option value="privacy">隱私保護</option>
                            <option value="industry">行業規範</option>
                            <option value="internal">內部政策</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="policy-severity">嚴重性:</label>
                        <select id="policy-severity">
                            <option value="LOW">低</option>
                            <option value="MEDIUM">中等</option>
                            <option value="HIGH">高</option>
                            <option value="CRITICAL">嚴重</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="policy-description">政策描述:</label>
                        <textarea id="policy-description" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="policy-rules">檢查規則 (JSON格式):</label>
                        <textarea id="policy-rules" rows="8"></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="policy-enabled">
                            啟用此政策
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="save-policy-btn" class="btn btn-primary">儲存政策</button>
                <button class="btn btn-secondary" onclick="closeModal('policy-edit-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('eqc.static', filename='js/compliance.js') }}"></script>
</body>
</html>