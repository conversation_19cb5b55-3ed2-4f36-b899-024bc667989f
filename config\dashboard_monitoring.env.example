# 統一監控儀表板環境變數配置示例
# 複製此文件為 .env 並根據需要修改配置值

# 環境設定
DASHBOARD_ENV=development

# 指標收集配置
DASHBOARD_METRICS_INTERVAL=30
DASHBOARD_ALERTS_INTERVAL=10
DASHBOARD_TRENDS_INTERVAL=300

# 告警閾值配置 - 郵件處理
DASHBOARD_EMAIL_PENDING_WARNING=10
DASHBOARD_EMAIL_PENDING_CRITICAL=50
DASHBOARD_EMAIL_PROCESSING_TIME_WARNING=300
DASHBOARD_EMAIL_PROCESSING_TIME_CRITICAL=900

# 告警閾值配置 - Dramatiq任務
DASHBOARD_DRAMATIQ_PENDING_WARNING=20
DASHBOARD_DRAMATIQ_PENDING_CRITICAL=100

# 告警閾值配置 - 系統資源
DASHBOARD_CPU_WARNING=80.0
DASHBOARD_CPU_CRITICAL=95.0
DASHBOARD_MEMORY_WARNING=80.0
DASHBOARD_MEMORY_CRITICAL=95.0
DASHBOARD_DISK_WARNING=80.0
DASHBOARD_DISK_CRITICAL=95.0

# 資料保留策略
DASHBOARD_METRICS_RETENTION=30
DASHBOARD_ALERTS_RETENTION=90

# WebSocket配置
DASHBOARD_WS_MAX_CONNECTIONS=100
DASHBOARD_WS_HEARTBEAT=30

# 資料庫配置
DASHBOARD_DB_PATH=outlook.db
DASHBOARD_DB_POOL_SIZE=10

# 安全配置
DASHBOARD_API_KEY=your_api_key_here
DASHBOARD_ENABLE_AUTH=false
DASHBOARD_ALLOWED_IPS=127.0.0.1,localhost
DASHBOARD_CORS_ORIGINS=*

# 通知配置
DASHBOARD_NOTIFICATION_CHANNELS=system
DASHBOARD_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# 郵件SMTP配置（如果啟用郵件通知）
DASHBOARD_EMAIL_SMTP_SERVER=smtp.gmail.com
DASHBOARD_EMAIL_SMTP_PORT=587
DASHBOARD_EMAIL_USERNAME=<EMAIL>
DASHBOARD_EMAIL_PASSWORD=your_app_password

# LINE通知配置（如果啟用LINE通知）
DASHBOARD_LINE_TOKEN=your_line_notify_token

# Webhook配置（如果啟用Webhook通知）
DASHBOARD_WEBHOOK_URLS=https://hooks.slack.com/services/xxx,https://discord.com/api/webhooks/xxx