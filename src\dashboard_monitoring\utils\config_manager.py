"""
統一監控儀表板 - 配置管理工具

提供配置驗證、備份、恢復和動態更新功能
"""

import os
import json
import shutil
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import asdict

from ..config.dashboard_config import DashboardConfig, get_dashboard_config, reload_dashboard_config


logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器
    
    提供完整的配置管理功能：
    - 配置驗證
    - 配置備份和恢復
    - 動態配置更新
    - 配置檔案管理
    """
    
    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path("config")
        self.backup_dir = self.config_dir / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
    
    def validate_config(self, config: DashboardConfig = None) -> Dict[str, Any]:
        """驗證配置
        
        Returns:
            Dict containing validation results
        """
        if config is None:
            config = get_dashboard_config()
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "config_summary": {}
        }
        
        try:
            # 驗證更新間隔
            if config.metrics_update_interval < 5:
                validation_result["errors"].append("指標更新間隔不能少於5秒")
            elif config.metrics_update_interval < 10:
                validation_result["warnings"].append("指標更新間隔建議不少於10秒")
            
            if config.alerts_check_interval < 3:
                validation_result["errors"].append("告警檢查間隔不能少於3秒")
            
            # 驗證告警閾值邏輯
            if config.alert_thresholds.email_pending_warning >= config.alert_thresholds.email_pending_critical:
                validation_result["errors"].append("郵件待處理警告閾值應小於嚴重閾值")
            
            if config.alert_thresholds.dramatiq_pending_warning >= config.alert_thresholds.dramatiq_pending_critical:
                validation_result["errors"].append("Dramatiq待處理警告閾值應小於嚴重閾值")
            
            if config.alert_thresholds.cpu_warning >= config.alert_thresholds.cpu_critical:
                validation_result["errors"].append("CPU警告閾值應小於嚴重閾值")
            
            if config.alert_thresholds.memory_warning >= config.alert_thresholds.memory_critical:
                validation_result["errors"].append("記憶體警告閾值應小於嚴重閾值")
            
            # 驗證資料庫配置
            if not config.database_config.db_path:
                validation_result["errors"].append("資料庫路徑不能為空")
            
            db_path = Path(config.database_config.db_path)
            if not db_path.is_absolute():
                db_path = Path.cwd() / db_path
            
            if not db_path.parent.exists():
                validation_result["warnings"].append(f"資料庫目錄不存在: {db_path.parent}")
            
            # 驗證WebSocket配置
            if config.websocket_config.max_connections <= 0:
                validation_result["errors"].append("WebSocket最大連接數必須大於0")
            
            if config.websocket_config.max_connections > 1000:
                validation_result["warnings"].append("WebSocket最大連接數過高，可能影響效能")
            
            # 驗證保留策略
            if config.retention_policies.metrics_history <= 0:
                validation_result["errors"].append("指標歷史保留天數必須大於0")
            
            if config.retention_policies.metrics_history > 365:
                validation_result["warnings"].append("指標歷史保留天數過長，可能影響儲存空間")
            
            # 生成配置摘要
            validation_result["config_summary"] = {
                "metrics_update_interval": config.metrics_update_interval,
                "alerts_check_interval": config.alerts_check_interval,
                "email_pending_thresholds": {
                    "warning": config.alert_thresholds.email_pending_warning,
                    "critical": config.alert_thresholds.email_pending_critical
                },
                "dramatiq_pending_thresholds": {
                    "warning": config.alert_thresholds.dramatiq_pending_warning,
                    "critical": config.alert_thresholds.dramatiq_pending_critical
                },
                "system_resource_thresholds": {
                    "cpu_warning": config.alert_thresholds.cpu_warning,
                    "cpu_critical": config.alert_thresholds.cpu_critical,
                    "memory_warning": config.alert_thresholds.memory_warning,
                    "memory_critical": config.alert_thresholds.memory_critical
                },
                "retention_days": {
                    "metrics": config.retention_policies.metrics_history,
                    "alerts": config.retention_policies.alerts_history
                },
                "websocket_max_connections": config.websocket_config.max_connections,
                "database_path": str(config.database_config.db_path)
            }
            
            # 設定驗證結果
            validation_result["is_valid"] = len(validation_result["errors"]) == 0
            
            if validation_result["is_valid"]:
                self.logger.info("配置驗證通過")
            else:
                self.logger.error(f"配置驗證失敗: {len(validation_result['errors'])} 個錯誤")
            
            if validation_result["warnings"]:
                self.logger.warning(f"配置驗證警告: {len(validation_result['warnings'])} 個警告")
            
        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"配置驗證異常: {str(e)}")
            self.logger.error(f"配置驗證異常: {e}")
        
        return validation_result
    
    def backup_config(self, config: DashboardConfig = None, backup_name: str = None) -> Path:
        """備份配置
        
        Args:
            config: 要備份的配置，預設使用當前配置
            backup_name: 備份名稱，預設使用時間戳
        
        Returns:
            備份檔案路徑
        """
        if config is None:
            config = get_dashboard_config()
        
        if backup_name is None:
            backup_name = f"dashboard_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_file = self.backup_dir / f"{backup_name}.json"
        
        try:
            config_data = config.to_dict()
            config_data["backup_info"] = {
                "created_at": datetime.now().isoformat(),
                "created_by": "ConfigManager",
                "original_env": config.env
            }
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已備份到: {backup_file}")
            return backup_file
            
        except Exception as e:
            self.logger.error(f"配置備份失敗: {e}")
            raise
    
    def restore_config(self, backup_file: Path) -> DashboardConfig:
        """恢復配置
        
        Args:
            backup_file: 備份檔案路徑
        
        Returns:
            恢復的配置物件
        """
        if not backup_file.exists():
            raise FileNotFoundError(f"備份檔案不存在: {backup_file}")
        
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 移除備份資訊
            config_data.pop("backup_info", None)
            
            # 創建新的配置物件
            env = config_data.get("env", "development")
            config = DashboardConfig(env=env)
            
            # 更新配置
            config._update_config_from_dict(config_data)
            
            # 驗證恢復的配置
            validation_result = self.validate_config(config)
            if not validation_result["is_valid"]:
                self.logger.warning(f"恢復的配置驗證失敗: {validation_result['errors']}")
            
            self.logger.info(f"配置已從備份恢復: {backup_file}")
            return config
            
        except Exception as e:
            self.logger.error(f"配置恢復失敗: {e}")
            raise
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有備份
        
        Returns:
            備份檔案資訊列表
        """
        backups = []
        
        try:
            for backup_file in self.backup_dir.glob("*.json"):
                try:
                    with open(backup_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    backup_info = config_data.get("backup_info", {})
                    
                    backups.append({
                        "name": backup_file.stem,
                        "file_path": str(backup_file),
                        "created_at": backup_info.get("created_at", "未知"),
                        "created_by": backup_info.get("created_by", "未知"),
                        "original_env": backup_info.get("original_env", "未知"),
                        "file_size": backup_file.stat().st_size,
                        "modified_time": datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat()
                    })
                    
                except Exception as e:
                    self.logger.warning(f"讀取備份檔案失敗 {backup_file}: {e}")
            
            # 按創建時間排序
            backups.sort(key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            self.logger.error(f"列出備份失敗: {e}")
        
        return backups
    
    def update_config(self, updates: Dict[str, Any], validate: bool = True) -> Dict[str, Any]:
        """動態更新配置
        
        Args:
            updates: 要更新的配置項
            validate: 是否驗證更新後的配置
        
        Returns:
            更新結果
        """
        result = {
            "success": False,
            "updated_fields": [],
            "errors": [],
            "warnings": []
        }
        
        try:
            config = get_dashboard_config()
            
            # 備份當前配置
            backup_file = self.backup_config(config, f"before_update_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            # 應用更新
            for key, value in updates.items():
                try:
                    if hasattr(config, key):
                        old_value = getattr(config, key)
                        setattr(config, key, value)
                        result["updated_fields"].append(f"{key}: {old_value} -> {value}")
                    else:
                        # 嘗試更新嵌套配置
                        if "." in key:
                            parts = key.split(".")
                            obj = config
                            for part in parts[:-1]:
                                obj = getattr(obj, part)
                            
                            old_value = getattr(obj, parts[-1])
                            setattr(obj, parts[-1], value)
                            result["updated_fields"].append(f"{key}: {old_value} -> {value}")
                        else:
                            result["errors"].append(f"未知的配置項: {key}")
                
                except Exception as e:
                    result["errors"].append(f"更新 {key} 失敗: {str(e)}")
            
            # 驗證更新後的配置
            if validate:
                validation_result = self.validate_config(config)
                if not validation_result["is_valid"]:
                    result["errors"].extend(validation_result["errors"])
                result["warnings"].extend(validation_result["warnings"])
            
            # 重新載入全域配置
            if not result["errors"]:
                reload_dashboard_config()
                result["success"] = True
                self.logger.info(f"配置更新成功: {len(result['updated_fields'])} 個欄位")
            else:
                self.logger.error(f"配置更新失敗: {result['errors']}")
            
        except Exception as e:
            result["errors"].append(f"配置更新異常: {str(e)}")
            self.logger.error(f"配置更新異常: {e}")
        
        return result
    
    def export_config(self, output_file: Path, config: DashboardConfig = None) -> None:
        """匯出配置到檔案
        
        Args:
            output_file: 輸出檔案路徑
            config: 要匯出的配置，預設使用當前配置
        """
        if config is None:
            config = get_dashboard_config()
        
        try:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            config_data = config.to_dict()
            config_data["export_info"] = {
                "exported_at": datetime.now().isoformat(),
                "exported_by": "ConfigManager",
                "version": "1.0.0"
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已匯出到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"配置匯出失敗: {e}")
            raise
    
    def get_environment_overrides(self) -> Dict[str, str]:
        """獲取當前環境變數覆蓋
        
        Returns:
            環境變數覆蓋字典
        """
        env_overrides = {}
        
        # 定義所有支援的環境變數
        env_vars = [
            "DASHBOARD_ENV",
            "DASHBOARD_METRICS_INTERVAL",
            "DASHBOARD_ALERTS_INTERVAL",
            "DASHBOARD_TRENDS_INTERVAL",
            "DASHBOARD_EMAIL_PENDING_WARNING",
            "DASHBOARD_EMAIL_PENDING_CRITICAL",
            "DASHBOARD_DRAMATIQ_PENDING_WARNING",
            "DASHBOARD_DRAMATIQ_PENDING_CRITICAL",
            "DASHBOARD_CPU_WARNING",
            "DASHBOARD_CPU_CRITICAL",
            "DASHBOARD_MEMORY_WARNING",
            "DASHBOARD_MEMORY_CRITICAL",
            "DASHBOARD_DISK_WARNING",
            "DASHBOARD_DISK_CRITICAL",
            "DASHBOARD_METRICS_RETENTION",
            "DASHBOARD_ALERTS_RETENTION",
            "DASHBOARD_WS_MAX_CONNECTIONS",
            "DASHBOARD_WS_HEARTBEAT",
            "DASHBOARD_DB_PATH",
            "DASHBOARD_DB_POOL_SIZE",
            "DASHBOARD_API_KEY",
            "DASHBOARD_ENABLE_AUTH",
            "DASHBOARD_ALLOWED_IPS",
            "DASHBOARD_CORS_ORIGINS",
            "DASHBOARD_NOTIFICATION_CHANNELS",
            "DASHBOARD_EMAIL_RECIPIENTS"
        ]
        
        for env_var in env_vars:
            if env_var in os.environ:
                env_overrides[env_var] = os.environ[env_var]
        
        return env_overrides


# 全域配置管理器實例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """獲取全域配置管理器實例"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager()
    
    return _config_manager