"""
郵件模組路由
處理所有郵件相關的路由和 API 端點
"""

from flask import Blueprint, render_template, jsonify, request, redirect, url_for
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.web_api.email_web_service import EmailWebService
from src.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
from src.infrastructure.logging.logger_manager import LoggerManager

# 創建藍圖
email_bp = Blueprint('email', __name__, 
                     template_folder='../templates',
                     static_folder='../static',
                     static_url_path='/static/email')

# 初始化服務
logger = LoggerManager().get_logger("EmailRoutes")
database = EmailDatabase()
web_service = EmailWebService(database)
sync_service = EmailSyncService(database)


@email_bp.route('/')
@email_bp.route('/inbox')
def inbox():
    """郵件收件匣主頁"""
    try:
        # 獲取統計資料
        stats = database.get_statistics()
        
        # 獲取寄件者列表
        senders = database.get_senders()
        
        return render_template('inbox.html',
                             statistics=stats,
                             senders=senders)
        
    except Exception as e:
        logger.error(f"載入收件匣失敗: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'載入頁面失敗: {str(e)}'}), 500


@email_bp.route('/detail/<int:email_id>')
def email_detail(email_id: int):
    """郵件詳情頁面"""
    try:
        email = database.get_email_by_id(email_id)
        if not email:
            return redirect(url_for('email.inbox'))

        # 標記為已讀
        database.mark_email_read(email_id)

        return render_template('email/email_detail.html', email=email)

    except Exception as e:
        logger.error(f"載入郵件詳情失敗: {e}")
        return redirect(url_for('email.inbox'))


# API 路由
@email_bp.route('/api/list')
def api_emails():
    """獲取郵件列表 API"""
    return jsonify(web_service.get_emails_api())


@email_bp.route('/api/<int:email_id>')
def api_email_detail(email_id: int):
    """獲取郵件詳情 API"""
    return jsonify(web_service.get_email_detail_api(email_id))


@email_bp.route('/api/<int:email_id>/failed-analysis')
def api_email_failed_analysis(email_id: int):
    """獲取解析失敗詳情 API"""
    return jsonify(web_service.get_email_failed_analysis_api(email_id))


@email_bp.route('/api/<int:email_id>', methods=['DELETE'])
def api_delete_email(email_id: int):
    """刪除郵件 API"""
    return jsonify(web_service.delete_email_api(email_id))


@email_bp.route('/api/batch-delete', methods=['POST'])
def api_batch_delete_emails():
    """批量刪除郵件 API"""
    try:
        data = request.get_json()
        if not data or 'email_ids' not in data:
            return jsonify({'success': False, 'message': '缺少 email_ids 參數'}), 400
        
        email_ids = data['email_ids']
        if not isinstance(email_ids, list) or not email_ids:
            return jsonify({'success': False, 'message': 'email_ids 必須是非空數組'}), 400
        
        # 使用批量刪除方法
        result = database.delete_emails_batch(email_ids)

        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'deleted_count': result['deleted_count'],
            'failed_count': result['failed_count'],
            'updated_senders': result.get('updated_senders', 0)
        })
        
    except Exception as e:
        logger.error(f"批量刪除郵件 API 失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/clear-all', methods=['POST'])
def api_clear_all_emails():
    """清空所有郵件 API"""
    try:
        # 確認操作
        data = request.get_json() or {}
        confirm = data.get('confirm', False)

        if not confirm:
            return jsonify({
                'success': False,
                'message': '需要確認操作：請在請求中包含 "confirm": true'
            }), 400

        # 執行清空操作
        result = database.clear_all_emails()

        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'deleted_emails': result['deleted_emails'],
            'deleted_senders': result['deleted_senders']
        })

    except Exception as e:
        logger.error(f"清空所有郵件 API 失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/batch-mark-read', methods=['POST'])
def api_batch_mark_read_emails():
    """批量標記已讀郵件 API"""
    try:
        data = request.get_json()
        if not data or 'email_ids' not in data:
            return jsonify({'success': False, 'message': '缺少 email_ids 參數'}), 400
        
        email_ids = data['email_ids']
        if not isinstance(email_ids, list) or not email_ids:
            return jsonify({'success': False, 'message': 'email_ids 必須是非空數組'}), 400
        
        # 執行批量標記已讀
        marked_count = 0
        failed_count = 0
        
        for email_id in email_ids:
            try:
                if database.mark_email_as_read(email_id):
                    marked_count += 1
                else:
                    failed_count += 1
                    logger.warning(f"標記郵件 {email_id} 已讀失敗")
            except Exception as e:
                failed_count += 1
                logger.error(f"標記郵件 {email_id} 已讀時發生錯誤: {e}")
        
        return jsonify({
            'success': True,
            'message': f'成功標記 {marked_count} 封郵件為已讀，失敗 {failed_count} 封',
            'marked_count': marked_count,
            'failed_count': failed_count
        })
        
    except Exception as e:
        logger.error(f"批量標記已讀郵件 API 失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/<int:email_id>/read', methods=['PUT'])
def api_mark_email_read(email_id: int):
    """標記單一郵件已讀/未讀 API"""
    try:
        data = request.get_json()
        if not data or 'is_read' not in data:
            return jsonify({'success': False, 'message': '缺少 is_read 參數'}), 400
        
        is_read = data['is_read']
        if not isinstance(is_read, bool):
            return jsonify({'success': False, 'message': 'is_read 必須是布林值'}), 400
        
        # 調用資料庫服務標記已讀
        if is_read:
            result = database.mark_email_as_read(email_id)
        else:
            result = database.mark_email_as_unread(email_id)
        
        if result:
            status_text = '已讀' if is_read else '未讀'
            return jsonify({
                'success': True,
                'message': f'郵件已標記為{status_text}',
                'email_id': email_id,
                'is_read': is_read
            })
        else:
            return jsonify({'success': False, 'message': '更新失敗'}), 500
        
    except Exception as e:
        logger.error(f"標記郵件 {email_id} 狀態失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/sync', methods=['POST'])
def api_sync():
    """同步郵件 API"""
    try:
        max_emails = request.json.get('max_emails', 100) if request.json else 100
        result = sync_service.sync_emails_background(max_emails)
        return jsonify(result)
    except Exception as e:
        logger.error(f"同步郵件 API 失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/sync/status')
def api_sync_status():
    """獲取同步狀態 API"""
    return jsonify(sync_service.get_sync_status())


@email_bp.route('/api/statistics')
def api_statistics():
    """獲取統計資料 API"""
    return jsonify(web_service.get_statistics_api())


@email_bp.route('/api/senders')
def api_senders():
    """獲取寄件者列表 API"""
    return jsonify(web_service.get_senders_api())


@email_bp.route('/api/search')
def api_search():
    """搜尋郵件 API"""
    return jsonify(web_service.search_emails_api())


@email_bp.route('/api/sync/auto/start', methods=['POST'])
def api_start_auto_sync():
    """啟動自動同步 API"""
    try:
        interval = request.json.get('interval', 60) if request.json else 60
        result = sync_service.start_auto_sync(interval)
        return jsonify(result)
    except Exception as e:
        logger.error(f"啟動自動同步失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/sync/auto/stop', methods=['POST'])
def api_stop_auto_sync():
    """停止自動同步 API"""
    return jsonify(sync_service.stop_auto_sync())


@email_bp.route('/api/connection/test')
def api_test_connection():
    """測試郵件伺服器連接 API"""
    try:
        import asyncio
        result = asyncio.run(sync_service.test_connection())
        return jsonify(result)
    except Exception as e:
        logger.error(f"測試連接失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500


@email_bp.route('/api/connection/status')
def api_connection_status():
    """獲取連接狀態 API"""
    return jsonify(sync_service.get_connection_status())


@email_bp.route('/api/<int:email_id>/process', methods=['POST'])
def api_process_email(email_id: int):
    """處理單一郵件 API - 使用統一的 ALL IN ONE 處理流程"""
    try:
        from src.infrastructure.adapters.database.models import EmailDB
        from src.application.services.unified_email_processor import UnifiedEmailProcessor
        from src.data_models.email_models import EmailData
        import asyncio

        with database.get_session() as session:
            # 查詢郵件資料
            email = session.query(EmailDB).filter(EmailDB.id == email_id).first()

            if not email:
                return jsonify({
                    'success': False,
                    'message': '郵件不存在'
                }), 404

            # 檢查是否已經處理過
            if email.is_processed:
                return jsonify({
                    'success': False,
                    'message': '此郵件已經處理過'
                })

            # 轉換為 EmailData 格式
            email_data = EmailData(
                message_id=email.message_id,
                subject=email.subject,
                sender=email.sender,
                received_time=email.received_time,
                body=email.body or "",
                attachments=[]  # 附件會在處理過程中自動獲取
            )

            # 使用統一的 ALL IN ONE 處理流程
            unified_processor = UnifiedEmailProcessor()

            processing_result = asyncio.run(unified_processor.process_email_complete(
                email_data=email_data,
                email_id=str(email_id),
                process_attachments=True,   # 處理附件
                process_vendor_files=True,  # 處理廠商檔案
                send_notifications=True,    # 發送LINE通知
                update_database=True        # 更新資料庫
            ))

            # 轉換為 API 回應格式
            if processing_result.is_success:
                return jsonify({
                    'success': True,
                    'message': f'ALL IN ONE 處理完成 (處理時間: {processing_result.processing_time:.2f}s)',
                    'email_id': email_id,
                    'vendor_code': processing_result.vendor_code,
                    'extraction_method': processing_result.parsing_result.extraction_method,
                    'attachment_processed': processing_result.attachment_result is not None,
                    'vendor_files_processed': processing_result.vendor_files_result is not None,
                    'notification_sent': processing_result.notification_sent,
                    'database_updated': processing_result.database_updated,
                    'processing_time': processing_result.processing_time
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'處理失敗: {processing_result.error_message}',
                    'email_id': email_id,
                    'error': processing_result.error_message
                })

    except Exception as e:
        logger.error(f"處理郵件 {email_id} 失敗: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500