# Python
__pycache__/
*/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env
venv_win_3_11_12/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
old_logs/

# Database
*.db
*.sqlite3

# Test Coverage
htmlcov/
.coverage
.pytest_cache/
.tox/

# Excel Files (原始 VBA 檔案，暫時保留但不追蹤變更)
*.xlsm
*.xlsx

# Temporary Files
*.tmp
*.temp
temp/
tmp/

# Node modules (如果有前端)
node_modules/

# Docker
.dockerignore

# Secrets
.env.local
.env.production
.env_key
secrets/
*.key
*.pem

# Cache
.cache/
.claude/settings.local.json

# Claude 工作目錄 - 不上傳的資料夾
.claude/reports/
.claude/logs/
.claude/backup/

# 整理目錄
bak_del/

# 測試資料目錄（包含敏感或大型測試檔案）
doc/

# 檔案上傳暫存目錄
D:/temp/
temp/uploads/
temp/extracted/

# 測試檔案和目錄（避免上傳測試檔案）
test_spd_conversion/
# 排除根目錄下的 tests_ 開頭的檔案和目錄
/tests_*
# 排除根目錄下的測試檔案，但允許 tests/ 目錄內的
/test_*.py
/simple_test_*.py
*_test_*/
test_data/
sample_data/

# 明確允許 tests/ 目錄及其所有內容（覆蓋上面的規則）
!tests/
!tests/**

# 解壓縮和上傳的檔案
*.zip
*.7z
*.rar
*.tar
*.gz
uploaded_*/
extracted_*/

# 處理結果暫存檔案
result_*/
output_*/
processed_*/

# 郵件附件目錄（可能包含敏感資料）
attachments/

logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/

# Tool configuration directories
.kiro/ n u l 
 
 
nul
tests/__pycache__/__init__.cpython-312.pyc
tests/api/__pycache__/test_api_endpoints.cpython-312-pytest-8.3.4.pyc
tests/api/__pycache__/test_api_import.cpython-312-pytest-8.3.4.pyc
tests/api/__pycache__/test_direct_api.cpython-312-pytest-8.3.4.pyc
tests/api/__pycache__/test_specific_endpoints.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_celery_basic.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_celery_eqc.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_celery_modes.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_production_mode.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_production_worker.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_worker_final.cpython-312-pytest-8.3.4.pyc
tests/celery/__pycache__/test_worker_status.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/__pycache__/__init__.cpython-312.pyc
tests/dependency_injection/__pycache__/conftest.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase1_foundation/step1.1_dependency_functions/__pycache__/test_dependency_functions.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase1_foundation/step1.1_dependency_functions/__pycache__/test_verification.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_categories.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_handling.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_recovery.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/phase2_high_priority/step2.1_execute_staging_task/__pycache__/test_execute_staging_task_refactored.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/shared/__pycache__/constants.cpython-312.pyc
tests/dependency_injection/shared/__pycache__/mock_services.cpython-312.pyc
tests/dependency_injection/shared/__pycache__/test_assertions.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/shared/__pycache__/test_helpers.cpython-312-pytest-8.3.4.pyc
tests/dependency_injection/shared/__pycache__/test_infrastructure.cpython-312-pytest-8.3.4.pyc
tests/unit/__pycache__/__init__.cpython-311.pyc
tests/unit/__pycache__/test_dashboard_file_collector.cpython-311-pytest-8.4.1.pyc
tests/unit/__pycache__/test_dashboard_system_collector.cpython-311-pytest-8.4.1.pyc
tests/unit/__pycache__/test_unified_task_interface.cpython-311-pytest-8.4.1.pyc
tests/__pycache__/__init__.cpython-311.pyc
tests/integration/__pycache__/__init__.cpython-311.pyc
tests/integration/__pycache__/test_dashboard_file_integration.cpython-311-pytest-8.4.1.pyc
tests/__pycache__/test_file_handlers.cpython-312-pytest-8.3.4.pyc
tests/unit/__pycache__/__init__.cpython-312.pyc
tests/unit/__pycache__/test_vendor_file_notification.cpython-312-pytest-8.3.4.pyc
tests/unit/application/__pycache__/__init__.cpython-312.pyc
tests/unit/application/__pycache__/test_email_processor.cpython-312-pytest-8.3.4.pyc
tests/unit/data_models/__pycache__/test_email_models.cpython-312-pytest-8.3.4.pyc
tests/unit/infrastructure/__pycache__/__init__.cpython-312.pyc
tests/unit/infrastructure/__pycache__/test_etd_parser_simple.cpython-312-pytest-8.3.4.pyc
tests/unit/infrastructure/adapters/email/__pycache__/test_email_whitelist_fix.cpython-312-pytest-8.3.4.pyc
tests/api/__pycache__/test_api_endpoints.cpython-311-pytest-8.4.1.pyc
tests/api/__pycache__/test_api_import.cpython-311-pytest-8.4.1.pyc
tests/api/__pycache__/test_direct_api.cpython-311-pytest-8.4.1.pyc
tests/api/__pycache__/test_specific_endpoints.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/__pycache__/__init__.cpython-311.pyc
tests/dependency_injection/__pycache__/conftest.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase1_foundation/step1.1_dependency_functions/__pycache__/test_dependency_functions.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase1_foundation/step1.1_dependency_functions/__pycache__/test_verification.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_categories.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_handling.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase1_foundation/step1.2_error_handling/__pycache__/test_error_recovery.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/phase2_high_priority/step2.1_execute_staging_task/__pycache__/test_execute_staging_task_refactored.cpython-311-pytest-8.4.1.pyc
tests/dependency_injection/shared/__pycache__/constants.cpython-311.pyc
tests/dependency_injection/shared/__pycache__/mock_services.cpython-311.pyc
tests/dependency_injection/shared/__pycache__/test_infrastructure.cpython-311-pytest-8.4.1.pyc
e --abbrev-ref HEAD
