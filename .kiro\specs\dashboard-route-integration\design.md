# Dashboard Route Integration Design

## Overview

This design document outlines the integration of the network browser dashboard (`http://localhost:5555/dashboard`) into the unified monitoring dashboard (`http://localhost:5555/dashboard/`). The goal is to consolidate all monitoring functionality into a single, comprehensive interface while maintaining backward compatibility and optimal user experience.

## Architecture

### Current State Analysis

**Existing Network Dashboard (`/dashboard`):**
- Location: `src/presentation/api/ui_routes.py` - `get_realtime_dashboard()`
- Features: Staging tasks, processing tasks, network status monitoring
- Technology: Flask route with HTML template generation
- Update mechanism: 30-second auto-refresh

**Existing Unified Dashboard (`/dashboard/`):**
- Location: `src/dashboard_monitoring/api/dashboard_html_api.py` - `dashboard_main()`
- Features: 6 monitoring blocks (email, Dramatiq, system, business, pipeline, vendor files)
- Technology: FastAPI with WebSocket real-time updates
- Template: `src/dashboard_monitoring/templates/dashboard_main.html`

### Integration Architecture

```mermaid
graph TB
    A[User Request: /dashboard] --> B[Flask Route Handler]
    B --> C[Redirect to /dashboard/]
    C --> D[FastAPI Unified Dashboard]
    D --> E[Dashboard HTML API]
    E --> F[Dashboard Main Template]
    F --> G[7 Monitoring Blocks]
    
    G --> H[Email Monitoring]
    G --> I[Dramatiq Monitoring]
    G --> J[System Monitoring]
    G --> K[Business Metrics]
    G --> L[Pipeline Monitoring]
    G --> M[Vendor Files]
    G --> N[Network Monitoring - NEW]
    
    N --> O[Staging Tasks]
    N --> P[Processing Tasks]
    N --> Q[Network Status]
```

## Components and Interfaces

### 1. Route Redirection Component

**Location:** `src/presentation/api/ui_routes.py`

```python
@router.get("/dashboard")
async def redirect_to_unified_dashboard():
    """Redirect to unified monitoring dashboard"""
    return RedirectResponse(url="/dashboard/", status_code=301)
```

**Purpose:** Provide seamless redirection from old network dashboard to unified dashboard.

### 2. Network Monitoring Integration

**Enhanced Unified Dashboard Template:**
- File: `src/dashboard_monitoring/templates/dashboard_main.html`
- Add 7th monitoring block for network monitoring
- Integrate staging tasks, processing tasks, and network status

**Network Data Collection:**
- File: `src/dashboard_monitoring/collectors/dashboard_network_collector.py`
- Collect staging task statistics
- Collect processing task statistics  
- Monitor network connectivity status

### 3. WebSocket Data Integration

**WebSocket Manager Enhancement:**
- File: `src/dashboard_monitoring/core/dashboard_websocket_manager.py`
- Add network metrics to WebSocket broadcast
- Support real-time network monitoring updates

**Monitoring Coordinator Integration:**
- File: `src/dashboard_monitoring/core/dashboard_monitoring_coordinator.py`
- Include network collector in data collection cycle
- Ensure network metrics are part of unified dashboard data

## Data Models

### Network Metrics Model

```python
@dataclass
class NetworkMetrics:
    # Staging Tasks
    staging_pending: int = 0
    staging_processing: int = 0
    staging_completed: int = 0
    staging_failed: int = 0
    staging_total: int = 0
    
    # Processing Tasks
    process_active: int = 0
    process_queued: int = 0
    process_completed: int = 0
    process_errors: int = 0
    process_total: int = 0
    
    # Network Status
    network_connection: str = "unknown"
    network_latency: str = "unknown"
    network_target_host: str = "************"
    network_overall_status: str = "unknown"
    
    timestamp: datetime = field(default_factory=datetime.now)
```

### Integration with Existing Models

The NetworkMetrics will be integrated into the existing DashboardMetrics structure:

```python
@dataclass
class DashboardMetrics:
    email_metrics: Optional[EmailMetrics] = None
    dramatiq_metrics: Optional[DramatiqMetrics] = None
    system_metrics: Optional[SystemMetrics] = None
    file_metrics: Optional[FileMetrics] = None
    business_metrics: Optional[BusinessMetrics] = None
    pipeline_metrics: Optional[PipelineMetrics] = None
    vendor_file_metrics: Optional[VendorFileMetrics] = None
    network_metrics: Optional[NetworkMetrics] = None  # NEW
```

## Error Handling

### Graceful Degradation Strategy

1. **Network Collector Failure:**
   - Display "Data Unavailable" in network monitoring block
   - Continue showing other monitoring blocks normally
   - Log error for debugging

2. **Redirection Failure:**
   - Fallback to showing network dashboard content inline
   - Provide manual link to unified dashboard

3. **WebSocket Connection Issues:**
   - Fallback to periodic refresh for network data
   - Show connection status indicator

### Error Isolation

- Network monitoring failures will not affect other monitoring blocks
- Each collector operates independently with error boundaries
- Failed data collection returns default/empty metrics

### WebSocket 連接管理

```python
class DashboardWebSocketManager:
    def __init__(self):
        self.clients: Dict[str, DashboardWebSocketClient] = {}
        self.metrics_collectors: Dict[str, Any] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str)
    async def disconnect(self, client_id: str)
    async def broadcast_metrics(self, metrics: Dict[str, Any])
    async def send_to_client(self, client_id: str, message: Dict[str, Any])
```

### WebSocket 訂閱類型

支援的訂閱類型包括：

```python
class DashboardSubscriptionType(Enum):
    ALL = "all"                    # 訂閱所有更新
    METRICS = "metrics"            # 只訂閱指標更新
    METRICS_UPDATE = "metrics_update"  # 指標更新（前端期望的類型）
    ALERTS = "alerts"              # 只訂閱告警更新
    ALERT = "alert"                # 告警（前端期望的類型）
    EMAIL_QUEUE = "email_queue"    # 只訂閱郵件佇列更新
    EMAIL_METRICS = "email_metrics"  # 郵件指標（前端期望的類型）
    DRAMATIQ_TASKS = "dramatiq_tasks"  # 只訂閱 Dramatiq 任務更新
    DRAMATIQ_METRICS = "dramatiq_metrics"  # Dramatiq 指標（前端期望的類型）
    SYSTEM_HEALTH = "system_health" # 只訂閱系統健康狀態更新
    SYSTEM_STATUS = "system_status"  # 系統狀態（前端期望的類型）
    SYSTEM_METRICS = "system_metrics"  # 系統指標（前端期望的類型）
    BUSINESS_METRICS = "business_metrics"  # 只訂閱業務指標更新
    FILE_METRICS = "file_metrics"  # 檔案指標（前端期望的類型）
    NETWORK_METRICS = "network_metrics"  # 網路指標
```

#### 訂閱類型說明

- **metrics_update**: 接收所有指標的實時更新
- **alert**: 接收系統告警和通知
- **system_status**: 接收系統狀態變化通知
- **email_metrics**: 接收郵件處理相關指標
- **dramatiq_metrics**: 接收 Dramatiq 任務隊列指標
- **system_metrics**: 接收系統資源使用指標
- **file_metrics**: 接收檔案處理相關指標
- **business_metrics**: 接收業務相關指標
- **network_metrics**: 接收網路監控指標

## Testing Strategy

### Unit Tests

1. **Route Redirection Tests:**
   - Test `/dashboard` redirects to `/dashboard/`
   - Verify correct HTTP status codes
   - Test redirection with query parameters

2. **Network Collector Tests:**
   - Test staging task data collection
   - Test processing task data collection
   - Test network status monitoring
   - Test error handling scenarios

3. **WebSocket Subscription Tests:**
   - Test all supported subscription types
   - Test invalid subscription type handling
   - Test subscription management (subscribe/unsubscribe)
   - Test error handling and feedback

4. **Integration Tests:**
   - Test network metrics integration in unified dashboard
   - Test WebSocket network data broadcasting
   - Test template rendering with network block

### End-to-End Tests

1. **User Journey Tests:**
   - Navigate from `/dashboard` to unified dashboard
   - Verify all 7 monitoring blocks display correctly
   - Test real-time updates for network metrics

2. **Performance Tests:**
   - Measure dashboard load time with network integration
   - Test WebSocket performance with network data
   - Verify no performance degradation

## Implementation Phases

### Phase 1: Network Data Collection
- Implement `DashboardNetworkCollector`
- Add `NetworkMetrics` model
- Integrate with monitoring coordinator

### Phase 2: Template Integration
- Add network monitoring block to unified dashboard template
- Implement JavaScript for network data handling
- Add WebSocket subscription for network metrics

### Phase 3: Route Redirection
- Implement redirection from `/dashboard` to `/dashboard/`
- Test backward compatibility
- Update any internal links

### Phase 4: Testing and Optimization
- Comprehensive testing of integrated functionality
- Performance optimization
- Documentation updates

## Migration Strategy

### Backward Compatibility

1. **Existing Bookmarks:**
   - `/dashboard` will redirect to `/dashboard/`
   - Users will automatically see the enhanced unified dashboard

2. **API Endpoints:**
   - Existing network monitoring APIs remain functional
   - New unified dashboard APIs supplement existing ones

3. **Feature Parity:**
   - All network dashboard features available in unified dashboard
   - Enhanced with real-time updates via WebSocket

### User Communication

1. **Automatic Redirection:**
   - Seamless transition for users
   - No manual action required

2. **Enhanced Features:**
   - Users gain access to comprehensive monitoring
   - Real-time updates improve user experience

## Security Considerations

### Access Control
- Maintain existing authentication/authorization patterns
- Network monitoring data follows same security model as other metrics

### Data Protection
- Network status information is non-sensitive system metrics
- No additional security concerns beyond existing dashboard

## Performance Considerations

### Data Collection Optimization
- Network collector uses caching to reduce API calls
- Parallel data collection with other collectors
- Efficient WebSocket broadcasting

### Frontend Performance
- Network monitoring block loads asynchronously
- Progressive enhancement for real-time features
- Minimal impact on existing dashboard performance

## Monitoring and Observability

### Metrics to Track
- Dashboard load times with network integration
- WebSocket connection stability
- Network data collection success rates
- User adoption of unified dashboard

### Logging Strategy
- Log network collector operations
- Track redirection usage
- Monitor WebSocket performance
- Error tracking for troubleshooting