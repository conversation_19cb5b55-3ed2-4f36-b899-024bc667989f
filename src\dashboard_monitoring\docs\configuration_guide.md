# 統一監控儀表板 - 配置指南

## 概述

統一監控儀表板提供完整的配置管理系統，支援環境變數覆蓋、配置驗證、備份恢復和動態更新功能。

## 配置結構

### 1. 更新間隔配置 (UpdateIntervals)

控制各種監控活動的執行頻率：

```python
metrics_collection: int = 30      # 指標收集間隔（秒）
alerts_evaluation: int = 10       # 告警評估間隔（秒）
trends_analysis: int = 300        # 趨勢分析間隔（秒，5分鐘）
websocket_heartbeat: int = 30     # WebSocket心跳間隔（秒）
database_cleanup: int = 3600      # 資料庫清理間隔（秒，1小時）
```

### 2. 告警閾值配置 (AlertThresholds)

定義各種監控指標的告警閾值：

#### 郵件處理告警
```python
email_pending_warning: int = 10           # 待處理郵件警告閾值
email_pending_critical: int = 50          # 待處理郵件嚴重閾值
email_processing_time_warning: int = 300  # 處理時間警告閾值（秒）
email_processing_time_critical: int = 900 # 處理時間嚴重閾值（秒）
email_failure_rate_warning: float = 0.1   # 失敗率警告閾值（10%）
email_failure_rate_critical: float = 0.25 # 失敗率嚴重閾值（25%）
```

#### Dramatiq任務告警
```python
dramatiq_pending_warning: int = 20        # 待處理任務警告閾值
dramatiq_pending_critical: int = 100      # 待處理任務嚴重閾值
dramatiq_failure_rate_warning: float = 0.1   # 失敗率警告閾值（10%）
dramatiq_failure_rate_critical: float = 0.25 # 失敗率嚴重閾值（25%）
```

#### 系統資源告警
```python
cpu_warning: float = 80.0      # CPU使用率警告閾值（%）
cpu_critical: float = 95.0     # CPU使用率嚴重閾值（%）
memory_warning: float = 80.0   # 記憶體使用率警告閾值（%）
memory_critical: float = 95.0  # 記憶體使用率嚴重閾值（%）
disk_warning: float = 80.0     # 磁碟使用率警告閾值（%）
disk_critical: float = 95.0    # 磁碟使用率嚴重閾值（%）
```

### 3. 資料保留策略 (RetentionPolicies)

控制歷史資料的保留時間：

```python
metrics_history: int = 30           # 指標歷史資料保留天數
alerts_history: int = 90            # 告警歷史保留天數
task_execution_history: int = 7     # 任務執行歷史保留天數
system_health_checks: int = 14      # 系統健康檢查保留天數
file_processing_stats: int = 30     # 檔案處理統計保留天數
```

### 4. WebSocket配置 (WebSocketConfig)

控制WebSocket連接和即時通訊：

```python
max_connections: int = 100          # 最大連接數
heartbeat_interval: int = 30        # 心跳間隔（秒）
connection_timeout: int = 300       # 連接超時（秒）
message_queue_size: int = 1000      # 訊息佇列大小
enable_compression: bool = True     # 啟用壓縮
```

### 5. 通知配置 (NotificationConfig)

控制告警通知的發送：

```python
enabled_channels: List[NotificationChannel] = [NotificationChannel.SYSTEM]
email_smtp_server: Optional[str] = None
email_smtp_port: int = 587
line_token: Optional[str] = None
webhook_urls: List[str] = []
alert_cooldown: int = 300           # 告警冷卻時間（秒）
```

## 環境變數覆蓋

系統支援通過環境變數覆蓋配置值：

### 基本配置
```bash
DASHBOARD_ENV=production                    # 環境設定
DASHBOARD_METRICS_INTERVAL=30              # 指標收集間隔
DASHBOARD_ALERTS_INTERVAL=10               # 告警評估間隔
DASHBOARD_TRENDS_INTERVAL=300              # 趨勢分析間隔
```

### 告警閾值
```bash
# 郵件處理告警
DASHBOARD_EMAIL_PENDING_WARNING=10
DASHBOARD_EMAIL_PENDING_CRITICAL=50
DASHBOARD_EMAIL_PROCESSING_TIME_WARNING=300
DASHBOARD_EMAIL_PROCESSING_TIME_CRITICAL=900

# Dramatiq任務告警
DASHBOARD_DRAMATIQ_PENDING_WARNING=20
DASHBOARD_DRAMATIQ_PENDING_CRITICAL=100

# 系統資源告警
DASHBOARD_CPU_WARNING=80.0
DASHBOARD_CPU_CRITICAL=95.0
DASHBOARD_MEMORY_WARNING=80.0
DASHBOARD_MEMORY_CRITICAL=95.0
DASHBOARD_DISK_WARNING=80.0
DASHBOARD_DISK_CRITICAL=95.0
```

### 資料保留和WebSocket
```bash
DASHBOARD_METRICS_RETENTION=30             # 指標保留天數
DASHBOARD_ALERTS_RETENTION=90              # 告警保留天數
DASHBOARD_WS_MAX_CONNECTIONS=100           # WebSocket最大連接數
DASHBOARD_WS_HEARTBEAT=30                  # WebSocket心跳間隔
```

### 資料庫和安全
```bash
DASHBOARD_DB_PATH=outlook.db               # 資料庫路徑
DASHBOARD_DB_POOL_SIZE=10                  # 連接池大小
DASHBOARD_API_KEY=your_api_key_here        # API金鑰
DASHBOARD_ENABLE_AUTH=false                # 啟用認證
```

### 通知設定
```bash
DASHBOARD_NOTIFICATION_CHANNELS=system,email,line
DASHBOARD_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
DASHBOARD_LINE_TOKEN=your_line_notify_token
```

## 配置管理命令行工具

系統提供完整的命令行工具來管理配置：

### 驗證配置
```bash
python src/dashboard_monitoring/cli/config_cli.py validate --verbose
```

### 備份配置
```bash
python src/dashboard_monitoring/cli/config_cli.py backup --name my_backup
```

### 恢復配置
```bash
python src/dashboard_monitoring/cli/config_cli.py restore config/backups/my_backup.json
```

### 列出備份
```bash
python src/dashboard_monitoring/cli/config_cli.py list-backups
```

### 顯示當前配置
```bash
python src/dashboard_monitoring/cli/config_cli.py show
python src/dashboard_monitoring/cli/config_cli.py show --format json
```

### 顯示環境變數覆蓋
```bash
python src/dashboard_monitoring/cli/config_cli.py env
```

### 匯出配置
```bash
python src/dashboard_monitoring/cli/config_cli.py export config/my_config.json
```

## 程式化配置管理

### 基本使用
```python
from dashboard_monitoring.config.dashboard_config import get_dashboard_config

# 獲取配置
config = get_dashboard_config()

# 訪問配置值
print(f"指標更新間隔: {config.metrics_update_interval} 秒")
print(f"郵件待處理警告閾值: {config.alert_thresholds.email_pending_warning}")
```

### 配置管理器
```python
from dashboard_monitoring.utils.config_manager import get_config_manager

# 獲取配置管理器
config_manager = get_config_manager()

# 驗證配置
validation_result = config_manager.validate_config()
if validation_result["is_valid"]:
    print("配置驗證通過")

# 備份配置
backup_file = config_manager.backup_config()
print(f"配置已備份到: {backup_file}")

# 動態更新配置
updates = {
    "metrics_update_interval": 15,
    "alert_thresholds.email_pending_warning": 5
}
result = config_manager.update_config(updates)
if result["success"]:
    print("配置更新成功")
```

## 配置檔案格式

系統支援JSON格式的配置檔案，預設位置為 `config/dashboard_monitoring.json`：

```json
{
  "env": "development",
  "alert_thresholds": {
    "email_pending_warning": 10,
    "email_pending_critical": 50,
    "dramatiq_pending_warning": 20,
    "dramatiq_pending_critical": 100,
    "cpu_warning": 80.0,
    "cpu_critical": 95.0,
    "memory_warning": 80.0,
    "memory_critical": 95.0
  },
  "update_intervals": {
    "metrics_collection": 30,
    "alerts_evaluation": 10,
    "trends_analysis": 300
  },
  "retention_policies": {
    "metrics_history": 30,
    "alerts_history": 90
  },
  "websocket_config": {
    "max_connections": 100,
    "heartbeat_interval": 30
  }
}
```

## 最佳實踐

### 1. 環境分離
- 開發環境使用較短的更新間隔和較低的閾值
- 生產環境使用較長的更新間隔和合理的閾值
- 使用環境變數覆蓋來區分不同環境

### 2. 告警閾值設定
- 警告閾值應該提前預警，但不要太敏感
- 嚴重閾值應該表示真正需要立即處理的情況
- 根據實際業務需求調整閾值

### 3. 資料保留策略
- 指標資料保留時間要平衡儲存空間和分析需求
- 告警歷史可以保留較長時間用於趨勢分析
- 定期清理過期資料以維護效能

### 4. 配置備份
- 在修改配置前先備份
- 定期備份重要配置
- 測試配置恢復流程

### 5. 監控配置變更
- 記錄所有配置變更
- 驗證配置變更的影響
- 建立配置變更審批流程

## 故障排除

### 常見問題

1. **配置驗證失敗**
   - 檢查告警閾值邏輯（警告 < 嚴重）
   - 確認更新間隔不小於最小值
   - 驗證資料庫路徑存在

2. **環境變數不生效**
   - 確認環境變數名稱正確
   - 檢查環境變數值的資料類型
   - 重新啟動應用程式

3. **配置檔案載入失敗**
   - 檢查JSON格式是否正確
   - 確認檔案路徑和權限
   - 查看日誌中的錯誤訊息

4. **備份恢復問題**
   - 確認備份檔案完整性
   - 檢查備份檔案格式版本
   - 驗證恢復後的配置

### 日誌和除錯

配置系統會記錄詳細的日誌資訊：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 配置載入日誌
# 配置驗證日誌
# 配置變更日誌
```

查看配置相關日誌來診斷問題。