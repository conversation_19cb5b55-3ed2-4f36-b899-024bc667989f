#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 3.2 Static Resource Migration Verification Script
Verify all static resources have been correctly migrated and template references are correct
"""

import os
import re
import json
from pathlib import Path
from collections import defaultdict

class MigrationVerifier:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.frontend_root = self.project_root / "frontend"
        self.src_root = self.project_root / "src"
        self.errors = []
        self.warnings = []
        self.stats = {
            "css_files": 0,
            "js_files": 0, 
            "image_files": 0,
            "template_files": 0,
            "missing_files": 0,
            "broken_references": 0
        }
        
    def log_error(self, message):
        self.errors.append(message)
        print(f"[ERROR] {message}")
        
    def log_warning(self, message):
        self.warnings.append(message)
        print(f"[WARNING] {message}")
        
    def log_success(self, message):
        print(f"[SUCCESS] {message}")
        
    def log_info(self, message):
        print(f"[INFO] {message}")
        
    def verify_old_location_cleared(self):
        """Verify original location has been cleared"""
        print("\n=== 1. Verify Original Location Cleared ===")
        old_static_path = self.src_root / "dashboard_monitoring" / "static"
        
        if not old_static_path.exists():
            self.log_error(f"原始静态目录不存在: {old_static_path}")
            return False
            
        # 检查是否只剩下 __init__.py 和目录结构
        for root, dirs, files in os.walk(old_static_path):
            for file in files:
                if file != "__init__.py":
                    file_path = Path(root) / file
                    self.log_error(f"原始位置仍有文件: {file_path}")
                    return False
                    
        self.log_success("原始位置已正确清空")
        return True
        
    def scan_static_files(self):
        """扫描前端目录中的静态文件"""
        print("\n=== 2. 扫描前端静态文件 ===")
        
        if not self.frontend_root.exists():
            self.log_error(f"前端目录不存在: {self.frontend_root}")
            return {}
            
        static_files = {}
        
        for module_dir in self.frontend_root.iterdir():
            if not module_dir.is_dir() or module_dir.name.startswith('.'):
                continue
                
            static_dir = module_dir / "static"
            if not static_dir.exists():
                continue
                
            module_files = {
                'css': [],
                'js': [],
                'images': [],
                'lib': []
            }
            
            # 扫描CSS文件
            css_dir = static_dir / "css"
            if css_dir.exists():
                for css_file in css_dir.glob("*.css"):
                    module_files['css'].append(css_file)
                    self.stats['css_files'] += 1
                    
            # 扫描JS文件
            js_dir = static_dir / "js"
            if js_dir.exists():
                for js_file in js_dir.rglob("*.js"):
                    module_files['js'].append(js_file)
                    self.stats['js_files'] += 1
                    
            # 扫描图片文件
            images_dir = static_dir / "images"
            if images_dir.exists():
                for img_file in images_dir.rglob("*"):
                    if img_file.is_file():
                        module_files['images'].append(img_file)
                        self.stats['image_files'] += 1
                        
            # 扫描库文件
            lib_dir = static_dir / "lib"
            if lib_dir.exists():
                for lib_file in lib_dir.rglob("*"):
                    if lib_file.is_file():
                        module_files['lib'].append(lib_file)
                        
            static_files[module_dir.name] = module_files
            
        # 打印统计信息
        self.log_info(f"发现静态文件:")
        self.log_info(f"  - CSS 文件: {self.stats['css_files']}")
        self.log_info(f"  - JS 文件: {self.stats['js_files']}")  
        self.log_info(f"  - 图片文件: {self.stats['image_files']}")
        
        return static_files
        
    def extract_template_references(self):
        """提取模板文件中的静态资源引用"""
        print("\n=== 3. 提取模板文件引用 ===")
        
        template_refs = defaultdict(list)
        url_for_pattern = re.compile(r"url_for\s*\(\s*['\"]([^'\"]+)\.static['\"],\s*filename\s*=\s*['\"]([^'\"]+)['\"]")
        
        for template_file in self.frontend_root.rglob("*.html"):
            self.stats['template_files'] += 1
            
            try:
                content = template_file.read_text(encoding='utf-8')
                matches = url_for_pattern.findall(content)
                
                for blueprint, filename in matches:
                    template_refs[template_file].append({
                        'blueprint': blueprint,
                        'filename': filename,
                        'expected_path': self.frontend_root / blueprint / "static" / filename
                    })
                    
            except Exception as e:
                self.log_warning(f"读取模板文件失败: {template_file} - {e}")
                
        self.log_info(f"扫描了 {self.stats['template_files']} 个模板文件")
        return dict(template_refs)
        
    def verify_template_references(self, template_refs):
        """验证模板引用的文件是否存在"""
        print("\n=== 4. 验证模板引用文件存在 ===")
        
        all_refs_valid = True
        
        for template_file, refs in template_refs.items():
            for ref in refs:
                expected_path = ref['expected_path']
                
                if not expected_path.exists():
                    self.log_error(f"缺少文件: {expected_path}")
                    self.log_error(f"  引用位置: {template_file}")
                    self.log_error(f"  蓝图: {ref['blueprint']}, 文件: {ref['filename']}")
                    self.stats['missing_files'] += 1
                    all_refs_valid = False
                else:
                    # 检查文件是否为空或只是占位符
                    try:
                        file_size = expected_path.stat().st_size
                        if file_size < 50:  # 小于50字节可能是占位符
                            content = expected_path.read_text(encoding='utf-8')
                            if 'placeholder' in content.lower() or content.strip().startswith('#'):
                                self.log_warning(f"占位符文件: {expected_path}")
                    except:
                        pass
                        
        if all_refs_valid:
            self.log_success("所有模板引用的文件都存在")
        else:
            self.log_error(f"发现 {self.stats['missing_files']} 个缺少的文件")
            
        return all_refs_valid
        
    def verify_key_migrated_files(self):
        """验证关键迁移文件"""
        print("\n=== 5. 验证关键迁移文件 ===")
        
        key_files = [
            "frontend/monitoring/static/css/dashboard_components.css",
            "frontend/monitoring/static/css/dashboard_main.css",
            "frontend/monitoring/static/js/dashboard_charts.js", 
            "frontend/monitoring/static/js/dashboard_main.js",
            "frontend/monitoring/static/js/dashboard_websocket.js",
            "frontend/monitoring/static/images/favicon.ico"
        ]
        
        all_exist = True
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                size = full_path.stat().st_size
                self.log_success(f"存在: {file_path} ({size} bytes)")
                
                # 检查文件内容质量
                if size < 100:
                    self.log_warning(f"文件可能为占位符: {file_path}")
                elif file_path.endswith('.css') and size > 10000:
                    self.log_success(f"CSS文件内容丰富: {file_path}")
                elif file_path.endswith('.js') and size > 5000:
                    self.log_success(f"JS文件内容丰富: {file_path}")
            else:
                self.log_error(f"缺少关键文件: {file_path}")
                all_exist = False
                
        return all_exist
        
    def check_file_contents(self):
        """检查文件内容质量"""
        print("\n=== 6. 检查文件内容质量 ===")
        
        # 检查重要CSS文件
        css_files = [
            self.frontend_root / "shared" / "static" / "css" / "global.css",
            self.frontend_root / "monitoring" / "static" / "css" / "dashboard_main.css"
        ]
        
        for css_file in css_files:
            if css_file.exists():
                try:
                    content = css_file.read_text(encoding='utf-8')
                    css_rules = content.count('{')
                    if css_rules > 50:
                        self.log_success(f"CSS文件内容充实: {css_file.name} ({css_rules} 规则)")
                    elif css_rules > 10:
                        self.log_info(f"CSS文件有基础内容: {css_file.name} ({css_rules} 规则)")
                    else:
                        self.log_warning(f"CSS文件内容较少: {css_file.name} ({css_rules} 规则)")
                except:
                    self.log_warning(f"无法读取CSS文件: {css_file}")
                    
        # 检查重要JS文件
        js_files = [
            self.frontend_root / "shared" / "static" / "js" / "main.js",
            self.frontend_root / "monitoring" / "static" / "js" / "dashboard_main.js"
        ]
        
        for js_file in js_files:
            if js_file.exists():
                try:
                    content = js_file.read_text(encoding='utf-8')
                    functions = content.count('function ')
                    classes = content.count('class ')
                    total_constructs = functions + classes
                    
                    if total_constructs > 20:
                        self.log_success(f"JS文件功能完整: {js_file.name} ({total_constructs} 函数/类)")
                    elif total_constructs > 5:
                        self.log_info(f"JS文件有基础功能: {js_file.name} ({total_constructs} 函数/类)")
                    else:
                        self.log_warning(f"JS文件功能较少: {js_file.name} ({total_constructs} 函数/类)")
                except:
                    self.log_warning(f"无法读取JS文件: {js_file}")
                    
    def test_flask_import(self):
        """测试Flask应用程序能否正常导入"""
        print("\n=== 7. 测试Flask应用导入 ===")
        
        try:
            # 尝试导入前端应用
            import sys
            sys.path.insert(0, str(self.frontend_root))
            
            # 检查app.py是否存在
            app_file = self.frontend_root / "app.py"
            if app_file.exists():
                self.log_success("前端 app.py 文件存在")
                
                # 尝试读取并简单验证
                content = app_file.read_text(encoding='utf-8')
                if 'Flask' in content and 'Blueprint' in content:
                    self.log_success("app.py 包含Flask和Blueprint导入")
                    return True
                else:
                    self.log_warning("app.py 可能缺少必要的导入")
                    
            else:
                self.log_error("前端 app.py 文件不存在")
                
        except Exception as e:
            self.log_error(f"Flask应用导入测试失败: {e}")
            
        return False
        
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("任务 3.2 静态资源迁移验证报告")
        print("="*60)
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"  - CSS 文件: {self.stats['css_files']}")
        print(f"  - JavaScript 文件: {self.stats['js_files']}")
        print(f"  - 图片文件: {self.stats['image_files']}")
        print(f"  - 模板文件: {self.stats['template_files']}")
        print(f"  - 缺少文件: {self.stats['missing_files']}")
        
        # 错误统计
        print(f"\n验证结果:")
        print(f"  - 错误数量: {len(self.errors)}")
        print(f"  - 警告数量: {len(self.warnings)}")
        
        # 评分计算
        total_checks = 7
        passed_checks = total_checks - len(self.errors)
        score = max(0, (passed_checks / total_checks) * 10)
        
        print(f"\n最终评分: {score:.1f}/10.0")
        
        # 状态判断
        if len(self.errors) == 0 and self.stats['missing_files'] == 0:
            status = "完全成功"
            recommendation = "可以继续下一任务"
        elif len(self.errors) <= 2:
            status = "基本成功"  
            recommendation = "建议修复小问题后继续"
        else:
            status = "需要修复"
            recommendation = "必须修复问题后才能继续"
            
        print(f"\n验证状态: {status}")
        print(f"建议: {recommendation}")
        
        # 详细错误
        if self.errors:
            print(f"\n错误列表:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
                
        if self.warnings:
            print(f"\n警告列表:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
                
        return {
            'score': score,
            'status': status,
            'recommendation': recommendation,
            'errors': self.errors,
            'warnings': self.warnings,
            'stats': self.stats
        }
        
    def run_verification(self):
        """运行完整验证"""
        print("开始任务 3.2 静态资源迁移验证")
        print(f"项目根目录: {self.project_root}")
        
        # 执行各项验证
        self.verify_old_location_cleared()
        static_files = self.scan_static_files()
        template_refs = self.extract_template_references()
        self.verify_template_references(template_refs)
        self.verify_key_migrated_files()
        self.check_file_contents()
        self.test_flask_import()
        
        # 生成报告
        return self.generate_report()

if __name__ == "__main__":
    project_root = Path(__file__).parent
    verifier = MigrationVerifier(project_root)
    result = verifier.run_verification()
    
    # 保存验证结果
    report_file = project_root / "migration_verification_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
        
    print(f"\n详细报告已保存至: {report_file}")