"""
統一監控儀表板 - 監控協調器

此模組實現了監控系統的核心協調器，負責管理所有監控活動，
包括資料收集、告警評估、WebSocket 廣播和系統健康檢查。

移除了 Celery 相關內容，統一使用 Dramatiq 架構。
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field

from ..models.dashboard_metrics_models import (
    DashboardMetrics, create_empty_dashboard_metrics
)
from ..models.dashboard_alert_models import (
    DashboardAlert
)
from ..config.dashboard_config import DashboardConfig


@dataclass
class CollectorStatus:
    """收集器狀態"""
    name: str
    is_enabled: bool = True
    last_collection: Optional[datetime] = None
    last_success: Optional[datetime] = None
    last_error: Optional[str] = None
    collection_count: int = 0
    error_count: int = 0
    avg_collection_time_ms: float = 0.0


class DashboardMonitoringCoordinator:
    """
    統一監控協調器 - 系統核心元件
    
    負責協調所有監控活動：
    - 管理資料收集器生命週期
    - 定期收集監控指標
    - 評估告警條件
    - 廣播即時更新
    - 維護系統健康狀態
    """
    
    def __init__(self, config: DashboardConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 系統狀態管理
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 服務實例 (通過依賴注入設定)
        self.alert_service: Optional[Any] = None
        self.websocket_manager: Optional[Any] = None
        self.repository: Optional[Any] = None
        self.cache_service: Optional[Any] = None
        
        # 資料收集器
        self.collectors: Dict[str, Any] = {}
        self.collector_status: Dict[str, CollectorStatus] = {}
        
        # 任務管理
        self.background_tasks: Set[asyncio.Task] = set()
        self.collection_lock = asyncio.Lock()
        self.shutdown_event = asyncio.Event()
        
        # 統計資訊
        self.stats = {
            'total_collections': 0,
            'successful_collections': 0,
            'failed_collections': 0,
            'alerts_generated': 0,
            'broadcasts_sent': 0,
            'uptime_seconds': 0
        }
        
        # 最後收集的資料
        self.last_metrics: Optional[DashboardMetrics] = None
        self.active_alerts: List[DashboardAlert] = []
    
    def set_dependencies(self, 
                        alert_service: Any = None,
                        websocket_manager: Any = None,
                        repository: Any = None,
                        cache_service: Any = None) -> None:
        """設定依賴服務 (依賴注入)"""
        self.alert_service = alert_service
        self.websocket_manager = websocket_manager
        self.repository = repository
        self.cache_service = cache_service
        
        self.logger.info("監控協調器依賴服務已設定")
    
    def register_collector(self, name: str, collector: Any) -> None:
        """註冊資料收集器"""
        self.collectors[name] = collector
        self.collector_status[name] = CollectorStatus(name=name)
        self.logger.info(f"已註冊收集器: {name}")
    
    def unregister_collector(self, name: str) -> None:
        """取消註冊資料收集器"""
        if name in self.collectors:
            del self.collectors[name]
            del self.collector_status[name]
            self.logger.info(f"已取消註冊收集器: {name}")
    
    async def start_monitoring(self) -> None:
        """
        啟動監控系統
        
        實現完整的生命週期管理：
        - 初始化所有服務
        - 啟動背景任務
        - 開始定期收集循環
        """
        if self.is_running:
            self.logger.warning("監控系統已在運行中")
            return
        
        try:
            self.logger.info("正在啟動統一監控儀表板...")
            
            # 設定系統狀態
            self.is_running = True
            self.start_time = datetime.now()
            self.shutdown_event.clear()
            
            # 驗證依賴服務
            await self._validate_dependencies()
            
            # 初始化收集器
            await self._initialize_collectors()
            
            # 啟動背景任務
            await self._start_background_tasks()
            
            # 執行初始收集
            await self._perform_initial_collection()
            
            self.logger.info(f"監控系統啟動成功，已註冊 {len(self.collectors)} 個收集器")
            
        except Exception as e:
            self.logger.error(f"監控系統啟動失敗: {e}")
            await self.stop_monitoring()
            raise
    
    async def stop_monitoring(self) -> None:
        """
        停止監控系統
        
        實現優雅關閉機制：
        - 停止所有背景任務
        - 清理資源
        - 保存最終狀態
        """
        if not self.is_running:
            self.logger.warning("監控系統未在運行")
            return
        
        try:
            self.logger.info("正在停止監控系統...")
            
            # 設定關閉標誌
            self.is_running = False
            self.shutdown_event.set()
            
            # 停止背景任務
            await self._stop_background_tasks()
            
            # 執行最終收集
            await self._perform_final_collection()
            
            # 清理收集器
            await self._cleanup_collectors()
            
            self.logger.info("監控系統已停止")
            
        except Exception as e:
            self.logger.error(f"監控系統停止時發生錯誤: {e}")
    
    async def collect_all_metrics(self) -> Optional[DashboardMetrics]:
        """
        收集所有監控指標
        
        實現並行收集機制：
        - 並行執行所有收集器
        - 錯誤隔離
        - 效能追蹤
        """
        if not self.is_running:
            return None
        
        async with self.collection_lock:
            start_time = datetime.now()
            collection_results = {}
            
            try:
                self.logger.debug("開始收集監控指標...")
                
                # 並行收集所有指標
                collection_tasks = []
                for name, collector in self.collectors.items():
                    if self.collector_status[name].is_enabled:
                        task = asyncio.create_task(
                            self._collect_from_single_collector(name, collector)
                        )
                        collection_tasks.append((name, task))
                
                # 等待所有收集任務完成
                for name, task in collection_tasks:
                    try:
                        result = await asyncio.wait_for(task, timeout=30.0)
                        collection_results[name] = result
                        self._update_collector_success(name)
                    except asyncio.TimeoutError:
                        self.logger.warning(f"收集器 {name} 超時")
                        self._update_collector_error(name, "收集超時")
                    except Exception as e:
                        self.logger.error(f"收集器 {name} 發生錯誤: {e}")
                        self._update_collector_error(name, str(e))
                
                # 組合收集結果
                metrics = await self._combine_collection_results(collection_results)
                
                # 更新統計
                collection_time = (datetime.now() - start_time).total_seconds() * 1000
                self._update_collection_stats(collection_time, True)
                
                # 儲存最後收集的資料
                self.last_metrics = metrics
                
                self.logger.debug(f"指標收集完成，耗時 {collection_time:.1f}ms")
                return metrics
                
            except Exception as e:
                collection_time = (datetime.now() - start_time).total_seconds() * 1000
                self._update_collection_stats(collection_time, False)
                self.logger.error(f"指標收集失敗: {e}")
                return None
    
    async def collect_email_metrics(self):
        """收集郵件監控指標"""
        if "email_collector" in self.collectors and self.collector_status["email_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("email_collector", self.collectors["email_collector"])
            except Exception as e:
                self.logger.error(f"收集郵件指標失敗: {e}")
                # 返回空的郵件指標
                from ..models.dashboard_metrics_models import EmailMetrics
                return EmailMetrics()
        else:
            from ..models.dashboard_metrics_models import EmailMetrics
            return EmailMetrics()
    
    async def collect_dramatiq_metrics(self):
        """收集 Dramatiq 任務指標"""
        if "dramatiq_collector" in self.collectors and self.collector_status["dramatiq_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("dramatiq_collector", self.collectors["dramatiq_collector"])
            except Exception as e:
                self.logger.error(f"收集 Dramatiq 指標失敗: {e}")
                # 返回空的 Dramatiq 指標
                from ..models.dashboard_metrics_models import DramatiqMetrics
                return DramatiqMetrics()
        else:
            from ..models.dashboard_metrics_models import DramatiqMetrics
            return DramatiqMetrics()
    
    async def collect_system_metrics(self):
        """收集系統資源指標"""
        if "system_collector" in self.collectors and self.collector_status["system_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("system_collector", self.collectors["system_collector"])
            except Exception as e:
                self.logger.error(f"收集系統指標失敗: {e}")
                # 返回空的系統指標
                from ..models.dashboard_metrics_models import SystemMetrics
                return SystemMetrics()
        else:
            from ..models.dashboard_metrics_models import SystemMetrics
            return SystemMetrics()
    
    async def collect_file_metrics(self):
        """收集檔案處理指標"""
        if "file_collector" in self.collectors and self.collector_status["file_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("file_collector", self.collectors["file_collector"])
            except Exception as e:
                self.logger.error(f"收集檔案指標失敗: {e}")
                # 返回空的檔案指標
                from ..models.dashboard_metrics_models import FileMetrics
                return FileMetrics()
        else:
            from ..models.dashboard_metrics_models import FileMetrics
            return FileMetrics()
    
    async def collect_business_metrics(self):
        """收集業務指標"""
        if "business_collector" in self.collectors and self.collector_status["business_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("business_collector", self.collectors["business_collector"])
            except Exception as e:
                self.logger.error(f"收集業務指標失敗: {e}")
                # 返回空的業務指標
                from ..models.dashboard_metrics_models import BusinessMetrics
                return BusinessMetrics()
        else:
            from ..models.dashboard_metrics_models import BusinessMetrics
            return BusinessMetrics()
    
    async def collect_pipeline_metrics(self):
        """收集 Pipeline 監控指標"""
        if "pipeline_collector" in self.collectors and self.collector_status["pipeline_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("pipeline_collector", self.collectors["pipeline_collector"])
            except Exception as e:
                self.logger.error(f"收集 Pipeline 指標失敗: {e}")
                # 返回空的 Pipeline 指標
                from ..models.dashboard_metrics_models import PipelineMetrics, create_pipeline_metrics
                return create_pipeline_metrics()
        else:
            from ..models.dashboard_metrics_models import PipelineMetrics, create_pipeline_metrics
            return create_pipeline_metrics()
    
    async def collect_vendor_file_metrics(self):
        """收集廠商文件監控指標"""
        if "vendor_file_collector" in self.collectors and self.collector_status["vendor_file_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("vendor_file_collector", self.collectors["vendor_file_collector"])
            except Exception as e:
                self.logger.error(f"收集廠商文件指標失敗: {e}")
                # 返回空的廠商文件指標
                from ..models.dashboard_metrics_models import VendorFileMetrics, create_vendor_file_metrics
                return create_vendor_file_metrics()
        else:
            from ..models.dashboard_metrics_models import VendorFileMetrics, create_vendor_file_metrics
            return create_vendor_file_metrics()
    
    async def collect_network_metrics(self):
        """收集網路監控指標"""
        if "network_collector" in self.collectors and self.collector_status["network_collector"].is_enabled:
            try:
                return await self._collect_from_single_collector("network_collector", self.collectors["network_collector"])
            except Exception as e:
                self.logger.error(f"收集網路監控指標失敗: {e}")
                # 返回空的網路監控指標
                from ..models.dashboard_metrics_models import NetworkMetrics, create_network_metrics
                return create_network_metrics()
        else:
            from ..models.dashboard_metrics_models import NetworkMetrics, create_network_metrics
            return create_network_metrics()
    
    async def collect_integrated_metrics(self):
        """收集整合監控指標 (包含 Pipeline 和 VendorFile)"""
        try:
            # 並行收集 Pipeline 和 VendorFile 指標
            pipeline_task = asyncio.create_task(self.collect_pipeline_metrics())
            vendor_file_task = asyncio.create_task(self.collect_vendor_file_metrics())
            
            pipeline_metrics, vendor_file_metrics = await asyncio.gather(
                pipeline_task, vendor_file_task, return_exceptions=True
            )
            
            # 處理異常結果
            if isinstance(pipeline_metrics, Exception):
                self.logger.error(f"Pipeline 指標收集異常: {pipeline_metrics}")
                from ..models.dashboard_metrics_models import create_pipeline_metrics
                pipeline_metrics = create_pipeline_metrics()
            
            if isinstance(vendor_file_metrics, Exception):
                self.logger.error(f"VendorFile 指標收集異常: {vendor_file_metrics}")
                from ..models.dashboard_metrics_models import create_vendor_file_metrics
                vendor_file_metrics = create_vendor_file_metrics()
            
            # 創建整合指標
            from ..models.dashboard_metrics_models import create_integrated_metrics
            integrated_metrics = create_integrated_metrics(
                pipeline_metrics=pipeline_metrics,
                vendor_file_metrics=vendor_file_metrics
            )
            
            return integrated_metrics
            
        except Exception as e:
            self.logger.error(f"收集整合指標失敗: {e}")
            from ..models.dashboard_metrics_models import create_integrated_metrics
            return create_integrated_metrics()
    
    async def evaluate_alerts(self, metrics: DashboardMetrics) -> List[DashboardAlert]:
        """評估告警條件"""
        if not self.alert_service:
            return []
        
        try:
            # 使用告警服務評估告警
            new_alerts = await self.alert_service.evaluate_alerts(metrics)
            
            # 更新活躍告警列表
            for alert in new_alerts:
                self.active_alerts.append(alert)
                self.stats['alerts_generated'] += 1
            
            return new_alerts
            
        except Exception as e:
            self.logger.error(f"告警評估失敗: {e}")
            return []
    
    async def broadcast_updates(self, metrics: DashboardMetrics, alerts: List[DashboardAlert]) -> None:
        """廣播即時更新"""
        if not self.websocket_manager:
            return
        
        try:
            # 準備廣播資料
            update_data = {
                'type': 'metrics_update',
                'timestamp': datetime.now().isoformat(),
                'metrics': metrics.to_dict(),
                'alerts': [alert.to_dict() for alert in alerts],
                'system_status': await self.get_current_status()
            }
            
            # 廣播到所有連接的客戶端
            await self.websocket_manager.broadcast(update_data)
            self.stats['broadcasts_sent'] += 1
            
        except Exception as e:
            self.logger.error(f"廣播更新失敗: {e}")
    
    async def get_current_status(self) -> Dict[str, Any]:
        """
        獲取當前系統狀態
        
        實現完整狀態報告：
        - 系統運行狀態
        - 收集器狀態
        - 效能統計
        - 告警摘要
        """
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        self.stats['uptime_seconds'] = uptime
        
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime_seconds': uptime,
            'health_status': self.get_health_status(),
            'collectors': {
                name: {
                    'is_enabled': status.is_enabled,
                    'last_collection': status.last_collection.isoformat() if status.last_collection else None,
                    'last_success': status.last_success.isoformat() if status.last_success else None,
                    'last_error': status.last_error,
                    'collection_count': status.collection_count,
                    'error_count': status.error_count,
                    'avg_collection_time_ms': status.avg_collection_time_ms
                }
                for name, status in self.collector_status.items()
            },
            'statistics': self.stats.copy(),
            'active_alerts_count': len(self.active_alerts),
            'websocket_connections': self.websocket_manager.get_connection_count() if self.websocket_manager else 0
        }
    
    def get_health_status(self) -> str:
        """
        獲取健康狀態
        
        實現多層次健康檢查：
        - 系統運行狀態
        - 收集器健康狀態
        - 服務可用性
        """
        if not self.is_running:
            return "stopped"
        
        # 檢查收集器健康狀態
        total_collectors = len(self.collectors)
        healthy_collectors = sum(
            1 for status in self.collector_status.values()
            if status.is_enabled and status.last_success and 
            (datetime.now() - status.last_success).total_seconds() < 300  # 5分鐘內有成功收集
        )
        
        if total_collectors == 0:
            return "no_collectors"
        
        health_ratio = healthy_collectors / total_collectors
        
        if health_ratio >= 0.8:
            return "healthy"
        elif health_ratio >= 0.5:
            return "degraded"
        else:
            return "unhealthy"
    
    def is_healthy(self) -> bool:
        """檢查系統是否健康"""
        health_status = self.get_health_status()
        return health_status in ["healthy", "degraded"]
    
    # 私有方法
    
    async def _validate_dependencies(self) -> None:
        """驗證依賴服務"""
        missing_deps = []
        
        if not self.alert_service:
            self.logger.warning("告警服務未設定，告警功能將不可用")
        
        if not self.websocket_manager:
            self.logger.warning("WebSocket 管理器未設定，即時更新功能將不可用")
        
        if not self.repository:
            self.logger.warning("資料庫存取層未設定，歷史資料功能將不可用")
        
        # 可以根據需要添加更多驗證邏輯
    
    async def _initialize_collectors(self) -> None:
        """初始化收集器"""
        for name, collector in self.collectors.items():
            try:
                if hasattr(collector, 'initialize'):
                    await collector.initialize()
                self.logger.info(f"收集器 {name} 初始化成功")
            except Exception as e:
                self.logger.error(f"收集器 {name} 初始化失敗: {e}")
                self.collector_status[name].is_enabled = False
    
    async def _start_background_tasks(self) -> None:
        """啟動背景任務"""
        # 主要監控循環
        main_task = asyncio.create_task(self._main_monitoring_loop())
        self.background_tasks.add(main_task)
        
        # 系統維護任務
        maintenance_task = asyncio.create_task(self._maintenance_loop())
        self.background_tasks.add(maintenance_task)
        
        # 告警清理任務
        alert_cleanup_task = asyncio.create_task(self._alert_cleanup_loop())
        self.background_tasks.add(alert_cleanup_task)
    
    async def _stop_background_tasks(self) -> None:
        """停止背景任務"""
        for task in self.background_tasks:
            task.cancel()
        
        # 等待所有任務完成
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        self.background_tasks.clear()
    
    async def _main_monitoring_loop(self) -> None:
        """主要監控循環"""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 收集指標
                metrics = await self.collect_all_metrics()
                
                if metrics:
                    # 評估告警
                    new_alerts = await self.evaluate_alerts(metrics)
                    
                    # 廣播更新
                    await self.broadcast_updates(metrics, new_alerts)
                    
                    # 儲存到資料庫 (如果有設定)
                    if self.repository:
                        await self._save_metrics_to_database(metrics)
                
                # 等待下次收集
                await asyncio.sleep(self.config.metrics_update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"監控循環發生錯誤: {e}")
                await asyncio.sleep(5)  # 錯誤後短暫等待
    
    async def _maintenance_loop(self) -> None:
        """系統維護循環"""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 清理過期的告警
                await self._cleanup_expired_alerts()
                
                # 更新收集器統計
                await self._update_collector_statistics()
                
                # 執行資料庫維護 (如果需要)
                if self.repository:
                    await self._perform_database_maintenance()
                
                # 每5分鐘執行一次維護
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"維護循環發生錯誤: {e}")
                await asyncio.sleep(60)
    
    async def _alert_cleanup_loop(self) -> None:
        """告警清理循環"""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 清理已解決的告警
                resolved_alerts = [alert for alert in self.active_alerts if alert.is_resolved()]
                for alert in resolved_alerts:
                    self.active_alerts.remove(alert)
                
                # 每分鐘檢查一次
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"告警清理循環發生錯誤: {e}")
                await asyncio.sleep(30)
    
    async def _collect_from_single_collector(self, name: str, collector: Any) -> Any:
        """從單一收集器收集資料"""
        start_time = datetime.now()
        
        try:
            # 執行收集
            if hasattr(collector, 'collect_metrics'):
                result = await collector.collect_metrics()
            elif hasattr(collector, 'collect'):
                result = await collector.collect()
            else:
                raise ValueError(f"收集器 {name} 沒有有效的收集方法")
            
            # 更新收集器狀態
            self.collector_status[name].last_collection = datetime.now()
            self.collector_status[name].collection_count += 1
            
            # 計算收集時間
            collection_time = (datetime.now() - start_time).total_seconds() * 1000
            status = self.collector_status[name]
            if status.avg_collection_time_ms == 0:
                status.avg_collection_time_ms = collection_time
            else:
                status.avg_collection_time_ms = (status.avg_collection_time_ms * 0.9) + (collection_time * 0.1)
            
            return result
            
        except Exception as e:
            self.collector_status[name].error_count += 1
            self.collector_status[name].last_error = str(e)
            raise
    
    async def _combine_collection_results(self, results: Dict[str, Any]) -> DashboardMetrics:
        """組合收集結果"""
        # 創建空的指標實例
        metrics = create_empty_dashboard_metrics()
        
        # 根據收集器名稱分配結果
        for name, result in results.items():
            if name == "email_collector" and result:
                metrics.email_metrics = result
            elif name == "dramatiq_collector" and result:
                metrics.dramatiq_metrics = result
            elif name == "system_collector" and result:
                metrics.system_metrics = result
            elif name == "file_collector" and result:
                metrics.file_metrics = result
            elif name == "business_collector" and result:
                metrics.business_metrics = result
            elif name == "pipeline_collector" and result:
                # Pipeline 指標需要特殊處理，因為它不是標準 DashboardMetrics 的一部分
                # 我們可以將其存儲在擴展屬性中或創建新的整合指標結構
                if not hasattr(metrics, 'pipeline_metrics'):
                    metrics.pipeline_metrics = result
            elif name == "vendor_file_collector" and result:
                # VendorFile 指標同樣需要特殊處理
                if not hasattr(metrics, 'vendor_file_metrics'):
                    metrics.vendor_file_metrics = result
            elif name == "network_collector" and result:
                # 網路監控指標處理
                if not hasattr(metrics, 'network_metrics'):
                    metrics.network_metrics = result
        
        return metrics
    
    def _update_collector_success(self, name: str) -> None:
        """更新收集器成功狀態"""
        if name in self.collector_status:
            self.collector_status[name].last_success = datetime.now()
            self.collector_status[name].last_error = None
    
    def _update_collector_error(self, name: str, error: str) -> None:
        """更新收集器錯誤狀態"""
        if name in self.collector_status:
            self.collector_status[name].last_error = error
            self.collector_status[name].error_count += 1
    
    def _update_collection_stats(self, collection_time_ms: float, success: bool) -> None:
        """更新收集統計"""
        self.stats['total_collections'] += 1
        
        if success:
            self.stats['successful_collections'] += 1
        else:
            self.stats['failed_collections'] += 1
        
        # 統計資訊已更新
    
    async def _perform_initial_collection(self) -> None:
        """執行初始收集"""
        try:
            self.logger.info("執行初始指標收集...")
            metrics = await self.collect_all_metrics()
            if metrics:
                self.logger.info("初始收集完成")
            else:
                self.logger.warning("初始收集失敗")
        except Exception as e:
            self.logger.error(f"初始收集發生錯誤: {e}")
    
    async def _perform_final_collection(self) -> None:
        """執行最終收集"""
        try:
            self.logger.info("執行最終指標收集...")
            metrics = await self.collect_all_metrics()
            if metrics and self.repository:
                await self._save_metrics_to_database(metrics)
        except Exception as e:
            self.logger.error(f"最終收集發生錯誤: {e}")
    
    async def _cleanup_collectors(self) -> None:
        """清理收集器"""
        for name, collector in self.collectors.items():
            try:
                if hasattr(collector, 'cleanup'):
                    await collector.cleanup()
            except Exception as e:
                self.logger.error(f"清理收集器 {name} 時發生錯誤: {e}")
    
    async def _cleanup_expired_alerts(self) -> None:
        """清理過期告警"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        expired_alerts = [
            alert for alert in self.active_alerts
            if alert.is_resolved() and alert.resolved_at and alert.resolved_at < cutoff_time
        ]
        
        for alert in expired_alerts:
            self.active_alerts.remove(alert)
    
    async def _update_collector_statistics(self) -> None:
        """更新收集器統計"""
        # 這裡可以添加更多統計邏輯
        pass
    
    async def _perform_database_maintenance(self) -> None:
        """執行資料庫維護"""
        try:
            if hasattr(self.repository, 'cleanup_old_data'):
                await self.repository.cleanup_old_data()
        except Exception as e:
            self.logger.error(f"資料庫維護失敗: {e}")
    
    async def _save_metrics_to_database(self, metrics: DashboardMetrics) -> None:
        """儲存指標到資料庫"""
        try:
            if hasattr(self.repository, 'save_metrics'):
                await self.repository.save_metrics(metrics)
        except Exception as e:
            self.logger.error(f"儲存指標到資料庫失敗: {e}")
# 全域實例管理
_monitoring_coordinator: Optional[DashboardMonitoringCoordinator] = None

def get_monitoring_coordinator(config: Optional[DashboardConfig] = None) -> DashboardMonitoringCoordinator:
    """獲取監控協調器單例"""
    global _monitoring_coordinator
    
    if _monitoring_coordinator is None:
        if config is None:
            from ..config.dashboard_config import DashboardConfig
            config = DashboardConfig()
        _monitoring_coordinator = DashboardMonitoringCoordinator(config)
    
    return _monitoring_coordinator