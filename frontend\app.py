#!/usr/bin/env python3
"""
前端 Flask 應用程式
重構後的模組化架構，為 Vue.js 遷移做準備
"""

import os
import sys
from pathlib import Path
from flask import Flask

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from frontend.config import Config, config
from frontend.shared.utils.error_handler import ErrorHandler


def create_app(config_name=None):
    """
    Flask 應用程式工廠函數
    
    Args:
        config_name: 配置名稱 ('development', 'testing', 'production')
        
    Returns:
        Flask: 配置好的 Flask 應用程式實例
    """
    # 決定使用的配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    config_class = config.get(config_name, config['default'])
    
    # 創建 Flask 應用程式
    app = Flask(__name__,
                template_folder='shared/templates',
                static_folder='shared/static')
    
    # 載入配置
    app.config.from_object(config_class)
    
    # 初始化應用程式配置
    config_class.init_app(app)
    
    # 註冊錯誤處理器
    ErrorHandler.register_error_handlers(app)
    
    # 註冊模組藍圖
    register_blueprints(app)
    
    # 設定模組化靜態資源路由
    setup_static_routes(app)
    
    return app


def register_blueprints(app):
    """
    註冊所有模組的藍圖
    
    Args:
        app: Flask 應用程式實例
    """
    # 導入各模組的藍圖
    from frontend.email.routes.email_routes import email_bp
    from frontend.analytics.routes.analytics_routes import analytics_bp
    from frontend.eqc.routes.eqc_routes import eqc_bp
    from frontend.tasks.routes.task_routes import task_bp
    from frontend.monitoring.routes.monitoring_routes import monitoring_bp
    from frontend.file_management.routes.file_routes import file_bp
    
    # 創建 shared 藍圖用於靜態資源
    from flask import Blueprint
    shared_bp = Blueprint('shared', __name__,
                          static_folder='shared/static',
                          static_url_path='/static/shared')
    
    # 註冊藍圖
    app.register_blueprint(shared_bp)  # 共用靜態資源
    app.register_blueprint(email_bp, url_prefix='/email')
    app.register_blueprint(analytics_bp, url_prefix='/analytics')
    app.register_blueprint(eqc_bp, url_prefix='/eqc')
    app.register_blueprint(task_bp, url_prefix='/tasks')
    app.register_blueprint(monitoring_bp, url_prefix='/monitoring')
    app.register_blueprint(file_bp, url_prefix='/files')
    
    # 註冊主頁路由（重定向到郵件模組）
    @app.route('/')
    def index():
        """主頁重定向到郵件收件匣"""
        from flask import redirect, url_for
        return redirect(url_for('email.inbox'))
    
    # Favicon 路由
    @app.route('/favicon.ico')
    def favicon():
        """提供 favicon"""
        from flask import send_from_directory
        return send_from_directory(app.static_folder, 'images/favicon.ico', mimetype='image/vnd.microsoft.icon')
    
    # 測試頁面路由
    @app.route('/test')
    def test_page():
        """測試頁面"""
        from flask import send_from_directory
        return send_from_directory('.', 'test_sync_fix.html')

    # 寄件者顯示調試頁面
    @app.route('/debug_sender_display.html')
    def debug_sender_display():
        """寄件者顯示調試頁面"""
        from flask import send_from_directory
        return send_from_directory('.', 'debug_sender_display.html')


def setup_static_routes(app):
    """
    設定模組化靜態資源路由
    
    Args:
        app: Flask 應用程式實例
    """
    from flask import send_from_directory
    
    # 為每個模組設定靜態資源路由
    static_mapping = app.config.get('STATIC_FOLDER_MAPPING', {})
    
    for module_name, static_path in static_mapping.items():
        # 創建唯一的端點名稱
        endpoint_name = f'serve_{module_name}_static'
        
        def create_static_handler(path):
            def serve_module_static(filename):
                """提供模組靜態資源"""
                return send_from_directory(path, filename)
            return serve_module_static
        
        # 註冊路由
        app.add_url_rule(
            f'/static/{module_name}/<path:filename>',
            endpoint_name,
            create_static_handler(static_path)
        )
    
    # 全域靜態資源路由（向後兼容）
    @app.route('/static/<path:filename>')
    def serve_global_static(filename):
        """提供全域靜態資源"""
        return send_from_directory('frontend/shared/static', filename)


if __name__ == '__main__':
    """
    開發模式啟動
    """
    # 從環境變數或配置獲取啟動參數
    config_name = os.environ.get('FLASK_ENV', 'development')
    app = create_app(config_name)
    
    # 從配置獲取主機和端口
    host = app.config.get('HOST', '0.0.0.0')
    port = app.config.get('PORT', 5000)
    debug = app.config.get('FLASK_DEBUG', True)
    
    app.run(host=host, port=port, debug=debug)