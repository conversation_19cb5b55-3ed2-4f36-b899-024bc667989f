#!/usr/bin/env python3
"""
簡單的連接測試腳本
"""

import urllib.request
import urllib.error
import json
from datetime import datetime

def test_http_endpoints():
    """測試 HTTP 端點"""
    endpoints = [
        "http://localhost:5555/health",
        "http://localhost:5555/dashboard/",
        "http://localhost:5555/dashboard/api/metrics/current",
        "http://localhost:5555/dashboard/api/alerts/active"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n🌐 測試端點: {endpoint}")
            
            with urllib.request.urlopen(endpoint, timeout=5) as response:
                status_code = response.getcode()
                print(f"✅ 狀態碼: {status_code}")
                
                if status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    data = response.read().decode('utf-8')
                    
                    if 'json' in content_type:
                        try:
                            json_data = json.loads(data)
                            print(f"📋 JSON 數據: {json.dumps(json_data, indent=2, ensure_ascii=False)[:300]}...")
                        except json.JSONDecodeError:
                            print(f"❌ JSON 解析失敗")
                    else:
                        print(f"📄 內容長度: {len(data)} 字符")
                        if len(data) < 500:
                            print(f"內容預覽: {data[:200]}...")
                else:
                    print(f"❌ 非 200 狀態碼")
                    
        except urllib.error.URLError as e:
            print(f"❌ URL 錯誤: {e}")
        except urllib.error.HTTPError as e:
            print(f"❌ HTTP 錯誤: {e.code} - {e.reason}")
        except Exception as e:
            print(f"❌ 其他錯誤: {e}")

def main():
    """主函數"""
    print("🔍 開始診斷 Dashboard 連接問題")
    print(f"⏰ 測試時間: {datetime.now()}")
    print("=" * 50)
    
    test_http_endpoints()
    
    print("\n" + "=" * 50)
    print("🏁 診斷完成")
    print("\n💡 如果所有 HTTP 端點都正常，問題可能出現在：")
    print("   1. WebSocket 連接處理")
    print("   2. 前端 JavaScript 錯誤")
    print("   3. 瀏覽器緩存問題")
    print("\n🔧 建議解決步驟：")
    print("   1. 清除瀏覽器緩存並重新載入")
    print("   2. 檢查瀏覽器開發者工具的控制台錯誤")
    print("   3. 檢查網路標籤中的 WebSocket 連接狀態")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 測試被中斷")
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")