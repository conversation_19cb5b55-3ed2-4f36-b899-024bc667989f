# 半導體測試資料郵件處理系統 - 完整流程分析

## 系統概述
這是一個從VBA Excel遷移到Python的半導體測試資料郵件處理系統，支援多個廠商（ETD、GTK、JCET、LINGSEN、XAHT等）的郵件自動化處理。

## 核心流程：收到郵件 → 解析郵件 → 下載遠端資料夾

### 階段一：郵件接收與同步
**主要檔案：**
- `src/infrastructure/adapters/email_inbox/email_sync_service.py`
- `src/infrastructure/adapters/email_reader_factory.py`
- `src/infrastructure/adapters/pop3/` (POP3郵件讀取)
- `src/infrastructure/adapters/outlook/` (Outlook整合)

**處理流程：**
```
1. EmailSyncService.sync_emails_once()
   ├── 初始化郵件讀取器 (POP3/Outlook)
   ├── 從郵件服務器讀取新郵件
   ├── 轉換為 EmailData 格式
   ├── 儲存到 SQLite 資料庫 (EmailDatabase)
   └── 觸發後續處理流程
```

**相關Python檔案：**
- `email_sync_service.py` - 郵件同步核心邏輯
- `email_reader_factory.py` - 郵件讀取器工廠
- `email_database.py` - 資料庫操作

### 階段二：郵件解析與廠商識別
**主要檔案：**
- `src/application/services/unified_email_processor.py`
- `src/infrastructure/parsers/base_parser.py`
- `src/infrastructure/parsers/[廠商]_parser.py`

**處理流程：**
```
2. UnifiedEmailProcessor.process_email_complete()
   ├── 廠商識別 (ParserFactory.identify_vendor())
   │   ├── 分析郵件主旨關鍵字
   │   ├── 分析寄件者資訊
   │   └── 選擇對應廠商解析器
   ├── 郵件內容解析
   │   ├── GTK: gtk_parser.py (ft hold, ft lot 關鍵字)
   │   ├── ETD: etd_parser.py (anf 關鍵字)
   │   ├── JCET: jcet_parser.py (jcet 關鍵字)
   │   ├── LINGSEN: lingsen_parser.py (lingsen 關鍵字)
   │   ├── XAHT: xaht_parser.py (tianshui, 西安 關鍵字)
   │   └── 其他廠商解析器...
   └── 提取關鍵資訊 (MO、LOT、良率等)
```

**廠商解析器檔案：**
- `base_parser.py` - 解析器基礎架構
- `gtk_parser.py` - GTK廠商解析器
- `etd_parser.py` - ETD廠商解析器
- `jcet_parser.py` - JCET廠商解析器
- `lingsen_parser.py` - LINGSEN廠商解析器
- `xaht_parser.py` - XAHT廠商解析器
- `llm_parser.py` - LLM智能解析器

### 階段三：附件處理與檔案下載
**主要檔案：**
- `src/infrastructure/adapters/attachments/attachment_manager.py`
- `src/infrastructure/adapters/email_inbox/sync_attachment_handler.py`
- `src/infrastructure/adapters/file_handlers/` (檔案處理器)

**處理流程：**
```
3. 附件處理流程
   ├── AttachmentManager.save_attachment()
   │   ├── 驗證附件格式和大小
   │   ├── 創建安全檔名
   │   ├── 儲存到本地目錄 (attachments/email_[id]/)
   │   └── 記錄附件資訊到資料庫
   ├── 檔案格式轉換
   │   ├── CSV檔案處理
   │   ├── Excel檔案處理 (OpenPyXL)
   │   ├── 壓縮檔案解壓縮
   │   └── 其他格式轉換
   └── 遠端資料夾下載
       ├── 解析郵件中的遠端路徑資訊
       ├── 建立網路連線 (SMB/FTP/HTTP)
       ├── 下載指定資料夾內容
       └── 同步到本地儲存
```

**相關Python檔案：**
- `attachment_manager.py` - 附件管理核心
- `sync_attachment_handler.py` - 同步附件處理
- `file_handlers/` - 各種檔案格式處理器

### 階段四：任務協調與排程
**主要檔案：**
- `src/services/email_processing_coordinator.py`
- `src/services/enhanced_task_scheduler.py`
- `src/services/concurrent_task_manager.py`

**處理流程：**
```
4. EmailProcessingCoordinator.process_email()
   ├── 廠商分流處理
   │   ├── GTK廠商 → 排程處理 (時間窗口控制)
   │   └── 其他廠商 → 立即處理
   ├── 並發任務管理
   │   ├── 任務優先級設定
   │   ├── 資源使用控制
   │   └── 錯誤恢復機制
   └── 處理結果統計
```

**相關Python檔案：**
- `email_processing_coordinator.py` - 郵件處理協調器
- `enhanced_task_scheduler.py` - 增強任務排程器
- `concurrent_task_manager.py` - 並發任務管理器

### 階段五：通知與資料庫更新
**主要檔案：**
- `src/infrastructure/adapters/notification/line_notification_service.py`
- `src/infrastructure/adapters/database/email_database.py`

**處理流程：**
```
5. 後處理階段
   ├── LINE通知發送
   │   ├── 處理成功通知
   │   ├── 錯誤警告通知
   │   └── 統計報告通知
   ├── 資料庫狀態更新
   │   ├── 標記郵件為已處理
   │   ├── 記錄處理結果
   │   ├── 更新統計資訊
   │   └── 儲存錯誤日誌
   └── 檔案清理
       ├── 清理暫存檔案
       ├── 歸檔處理完成的檔案
       └── 維護儲存空間
```

## 服務啟動與整合

### 主要啟動檔案：
- `start_integrated_services.py` - 企業級整合服務啟動程式
- `frontend/app.py` - Flask 前端應用程式 (模組化架構)

### 服務架構：
```
統一端口 5000
├── Flask 服務 (郵件收件夾管理)
├── FastAPI 服務 (FT-EQC 處理)
├── 任務排程器 (背景處理)
├── 監控服務 (Prometheus + Grafana)
└── 管理介面 (/admin)
```

## 關鍵配置檔案：
- `.env` - 環境變數配置
- `requirements.txt` - Python依賴套件
- `docker-compose.yml` - Docker服務編排
- `Makefile` - 開發指令

## 資料流向圖：
```
郵件服務器 (POP3/Outlook)
    ↓
EmailSyncService (郵件同步)
    ↓
EmailDatabase (SQLite儲存)
    ↓
UnifiedEmailProcessor (統一處理器)
    ↓
ParserFactory (廠商識別)
    ↓
[廠商]Parser (內容解析)
    ↓
AttachmentManager (附件處理)
    ↓
遠端資料夾下載
    ↓
LineNotificationService (通知發送)
    ↓
處理完成
```

## 支援的廠商與識別關鍵字：
- **ETD**: `anf` 關鍵字
- **GTK**: `ft hold`, `ft lot` 關鍵字  
- **JCET**: `jcet` 關鍵字
- **LINGSEN**: `lingsen` 關鍵字
- **XAHT**: `tianshui`, `西安` 關鍵字
- **其他**: LLM智能識別

## 技術堆疊：
- **後端**: Python 3.9+, Flask 2.3.3, FastAPI 0.104.1
- **資料庫**: SQLite (開發), PostgreSQL (生產)
- **資料處理**: Pandas, OpenPyXL, XlsxWriter
- **測試**: Pytest, Playwright
- **容器化**: Docker, Docker Compose
- **監控**: Prometheus, Grafana, Loguru

這個系統實現了完整的郵件自動化處理流程，從接收郵件到最終的資料處理和通知，全程自動化運行。