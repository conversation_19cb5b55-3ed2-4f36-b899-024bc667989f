"""統一監控儀表板配置系統

提供完整的配置管理，包括：
- 環境變數覆蓋機制
- 配置驗證和預設值系統
- 動態配置更新支援
- 多環境配置管理

符合需求 12, 13 的配置要求
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警等級枚舉"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class NotificationChannel(Enum):
    """通知管道枚舉"""
    EMAIL = "email"
    LINE = "line"
    SYSTEM = "system"
    WEBHOOK = "webhook"


@dataclass
class AlertThresholds:
    """告警閾值配置
    
    根據業務需求設定的告警閾值：
    - 郵件佇列: 待處理 >10 警告, >50 嚴重
    - Dramatiq任務: 待處理 >20 警告, >100 嚴重
    - 系統資源: CPU/記憶體 >80% 警告, >95% 嚴重
    - 失敗率: >10% 警告, >25% 嚴重
    """
    # 郵件佇列告警閾值 (符合指導原則: >10 警告, >50 嚴重)
    email_pending_warning: int = 10
    email_pending_critical: int = 50
    email_processing_time_warning: int = 300  # 5分鐘
    email_processing_time_critical: int = 900  # 15分鐘
    email_failure_rate_warning: float = 0.1  # 10%
    email_failure_rate_critical: float = 0.25  # 25%
    
    # Dramatiq任務告警閾值 (符合指導原則: >20 警告, >100 嚴重)
    dramatiq_pending_warning: int = 20
    dramatiq_pending_critical: int = 100
    dramatiq_failure_rate_warning: float = 0.1  # 10%
    dramatiq_failure_rate_critical: float = 0.25  # 25%
    dramatiq_task_timeout_warning: int = 1800  # 30分鐘
    dramatiq_task_timeout_critical: int = 3600  # 60分鐘
    
    # 系統資源告警閾值 (符合指導原則: >80% 警告, >95% 嚴重)
    cpu_warning: float = 80.0
    cpu_critical: float = 95.0
    memory_warning: float = 80.0  # 調整為80%以符合指導原則
    memory_critical: float = 95.0
    disk_warning: float = 80.0   # 調整為80%以符合指導原則
    disk_critical: float = 95.0
    
    # 檔案處理告警閾值
    temp_size_warning: int = 1000  # 1GB (MB)
    temp_size_critical: int = 5000  # 5GB (MB)
    file_processing_time_warning: int = 600  # 10分鐘
    file_processing_time_critical: int = 1800  # 30分鐘
    
    # 資料庫告警閾值
    db_query_time_warning: float = 5.0  # 5秒
    db_query_time_critical: float = 10.0  # 10秒
    db_connection_warning: int = 80  # 80%連接池使用率
    db_connection_critical: int = 95  # 95%連接池使用率


@dataclass
class UpdateIntervals:
    """更新間隔配置（秒）"""
    metrics_collection: int = 30  # 指標收集間隔
    alerts_evaluation: int = 10  # 告警評估間隔
    trends_analysis: int = 300  # 趨勢分析間隔（5分鐘）
    websocket_heartbeat: int = 30  # WebSocket心跳間隔
    database_cleanup: int = 3600  # 資料庫清理間隔（1小時）


@dataclass
class RetentionPolicies:
    """資料保留策略（天）"""
    metrics_history: int = 30  # 指標歷史資料保留30天
    alerts_history: int = 90  # 告警歷史保留90天
    task_execution_history: int = 7  # 任務執行歷史保留7天
    system_health_checks: int = 14  # 系統健康檢查保留14天
    file_processing_stats: int = 30  # 檔案處理統計保留30天


@dataclass
class WebSocketConfig:
    """WebSocket配置"""
    max_connections: int = 100  # 最大連接數
    heartbeat_interval: int = 30  # 心跳間隔（秒）
    connection_timeout: int = 300  # 連接超時（秒）
    message_queue_size: int = 1000  # 訊息佇列大小
    enable_compression: bool = True  # 啟用壓縮


@dataclass
class DisplayConfig:
    """顯示配置"""
    default_time_range: str = "1h"  # 預設時間範圍: "1h", "6h", "24h", "7d"
    max_chart_data_points: int = 100  # 圖表最大資料點數
    refresh_interval: int = 5  # 前端重新整理間隔（秒）
    enable_dark_theme: bool = False  # 啟用深色主題
    show_debug_info: bool = False  # 顯示除錯資訊


@dataclass
class NotificationConfig:
    """通知配置"""
    enabled_channels: List[NotificationChannel] = field(default_factory=lambda: [
        NotificationChannel.SYSTEM
    ])
    email_smtp_server: Optional[str] = None
    email_smtp_port: int = 587
    email_username: Optional[str] = None
    email_password: Optional[str] = None
    email_recipients: List[str] = field(default_factory=list)
    line_token: Optional[str] = None
    webhook_urls: List[str] = field(default_factory=list)
    alert_cooldown: int = 300  # 告警冷卻時間（秒）


@dataclass
class DatabaseConfig:
    """資料庫配置"""
    db_path: str = "outlook.db"  # 資料庫檔案路徑
    connection_pool_size: int = 10  # 連接池大小
    connection_timeout: int = 30  # 連接超時（秒）
    query_timeout: int = 30  # 查詢超時（秒）
    enable_wal_mode: bool = True  # 啟用WAL模式
    backup_enabled: bool = True  # 啟用備份
    backup_interval: int = 86400  # 備份間隔（秒，24小時）


@dataclass
class SecurityConfig:
    """安全配置"""
    enable_authentication: bool = False  # 啟用認證
    api_key: Optional[str] = None  # API金鑰
    allowed_ips: List[str] = field(default_factory=lambda: ["127.0.0.1", "localhost"])
    enable_rate_limiting: bool = True  # 啟用速率限制
    rate_limit_requests: int = 100  # 每分鐘請求限制
    enable_cors: bool = True  # 啟用CORS
    cors_origins: List[str] = field(default_factory=lambda: ["*"])


class DashboardConfig:
    """統一監控儀表板配置管理器
    
    提供完整的配置管理功能：
    - 環境變數覆蓋機制
    - 配置驗證和預設值
    - 動態配置更新
    - 多環境支援
    """
    
    def __init__(self, env: str = "development", config_file: Optional[Path] = None):
        self.env = env
        self.config_file = config_file or Path("config/dashboard_monitoring.json")
        
        # 初始化各項配置
        self.alert_thresholds = AlertThresholds()
        self.update_intervals = UpdateIntervals()
        self.retention_policies = RetentionPolicies()
        self.websocket_config = WebSocketConfig()
        self.display_config = DisplayConfig()
        self.notification_config = NotificationConfig()
        self.database_config = DatabaseConfig()
        self.security_config = SecurityConfig()
        
        # 載入配置檔案（如果存在）
        self._load_config_file()
        
        # 應用環境變數覆蓋
        self._apply_environment_overrides()
        
        # 驗證配置
        self._validate_config()
    
    def _load_config_file(self) -> None:
        """載入配置檔案"""
        if not self.config_file.exists():
            logger.info(f"配置檔案 {self.config_file} 不存在，使用預設配置")
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新各項配置
            self._update_config_from_dict(config_data)
            logger.info(f"成功載入配置檔案: {self.config_file}")
            
        except Exception as e:
            logger.error(f"載入配置檔案失敗: {e}")
            logger.info("使用預設配置")
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]) -> None:
        """從字典更新配置"""
        if "alert_thresholds" in config_data:
            self._update_dataclass(self.alert_thresholds, config_data["alert_thresholds"])
        
        if "update_intervals" in config_data:
            self._update_dataclass(self.update_intervals, config_data["update_intervals"])
        
        if "retention_policies" in config_data:
            self._update_dataclass(self.retention_policies, config_data["retention_policies"])
        
        if "websocket_config" in config_data:
            self._update_dataclass(self.websocket_config, config_data["websocket_config"])
        
        if "display_config" in config_data:
            self._update_dataclass(self.display_config, config_data["display_config"])
        
        if "notification_config" in config_data:
            self._update_notification_config(config_data["notification_config"])
        
        if "database_config" in config_data:
            self._update_dataclass(self.database_config, config_data["database_config"])
        
        if "security_config" in config_data:
            self._update_dataclass(self.security_config, config_data["security_config"])
    
    def _update_dataclass(self, obj: Any, data: Dict[str, Any]) -> None:
        """更新dataclass物件"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _update_notification_config(self, data: Dict[str, Any]) -> None:
        """更新通知配置"""
        if "enabled_channels" in data:
            self.notification_config.enabled_channels = [
                NotificationChannel(channel) for channel in data["enabled_channels"]
            ]
        
        # 更新其他通知配置
        for key, value in data.items():
            if key != "enabled_channels" and hasattr(self.notification_config, key):
                setattr(self.notification_config, key, value)
    
    def _apply_environment_overrides(self) -> None:
        """應用環境變數覆蓋"""
        # 告警閾值環境變數覆蓋
        env_mappings = {
            # 郵件告警閾值
            "DASHBOARD_EMAIL_PENDING_WARNING": ("alert_thresholds", "email_pending_warning", int),
            "DASHBOARD_EMAIL_PENDING_CRITICAL": ("alert_thresholds", "email_pending_critical", int),
            "DASHBOARD_EMAIL_PROCESSING_TIME_WARNING": ("alert_thresholds", "email_processing_time_warning", int),
            "DASHBOARD_EMAIL_PROCESSING_TIME_CRITICAL": ("alert_thresholds", "email_processing_time_critical", int),
            
            # Dramatiq告警閾值
            "DASHBOARD_DRAMATIQ_PENDING_WARNING": ("alert_thresholds", "dramatiq_pending_warning", int),
            "DASHBOARD_DRAMATIQ_PENDING_CRITICAL": ("alert_thresholds", "dramatiq_pending_critical", int),
            
            # 系統資源告警閾值
            "DASHBOARD_CPU_WARNING": ("alert_thresholds", "cpu_warning", float),
            "DASHBOARD_CPU_CRITICAL": ("alert_thresholds", "cpu_critical", float),
            "DASHBOARD_MEMORY_WARNING": ("alert_thresholds", "memory_warning", float),
            "DASHBOARD_MEMORY_CRITICAL": ("alert_thresholds", "memory_critical", float),
            "DASHBOARD_DISK_WARNING": ("alert_thresholds", "disk_warning", float),
            "DASHBOARD_DISK_CRITICAL": ("alert_thresholds", "disk_critical", float),
            
            # 更新間隔
            "DASHBOARD_METRICS_INTERVAL": ("update_intervals", "metrics_collection", int),
            "DASHBOARD_ALERTS_INTERVAL": ("update_intervals", "alerts_evaluation", int),
            "DASHBOARD_TRENDS_INTERVAL": ("update_intervals", "trends_analysis", int),
            
            # 資料保留策略
            "DASHBOARD_METRICS_RETENTION": ("retention_policies", "metrics_history", int),
            "DASHBOARD_ALERTS_RETENTION": ("retention_policies", "alerts_history", int),
            
            # WebSocket配置
            "DASHBOARD_WS_MAX_CONNECTIONS": ("websocket_config", "max_connections", int),
            "DASHBOARD_WS_HEARTBEAT": ("websocket_config", "heartbeat_interval", int),
            
            # 資料庫配置
            "DASHBOARD_DB_PATH": ("database_config", "db_path", str),
            "DASHBOARD_DB_POOL_SIZE": ("database_config", "connection_pool_size", int),
            
            # 安全配置
            "DASHBOARD_API_KEY": ("security_config", "api_key", str),
            "DASHBOARD_ENABLE_AUTH": ("security_config", "enable_authentication", bool),
        }
        
        for env_var, (config_section, attr_name, type_func) in env_mappings.items():
            if env_var in os.environ:
                try:
                    value = type_func(os.environ[env_var])
                    config_obj = getattr(self, config_section)
                    setattr(config_obj, attr_name, value)
                    logger.debug(f"環境變數覆蓋: {env_var} = {value}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"環境變數 {env_var} 值無效: {e}")
        
        # 特殊處理列表類型的環境變數
        self._apply_list_env_overrides()
    
    def _apply_list_env_overrides(self) -> None:
        """應用列表類型的環境變數覆蓋"""
        # 通知管道
        if "DASHBOARD_NOTIFICATION_CHANNELS" in os.environ:
            try:
                channels = os.environ["DASHBOARD_NOTIFICATION_CHANNELS"].split(",")
                self.notification_config.enabled_channels = [
                    NotificationChannel(channel.strip()) for channel in channels
                ]
            except ValueError as e:
                logger.warning(f"通知管道環境變數無效: {e}")
        
        # 郵件收件人
        if "DASHBOARD_EMAIL_RECIPIENTS" in os.environ:
            recipients = [
                email.strip() 
                for email in os.environ["DASHBOARD_EMAIL_RECIPIENTS"].split(",")
            ]
            self.notification_config.email_recipients = recipients
        
        # CORS來源
        if "DASHBOARD_CORS_ORIGINS" in os.environ:
            origins = [
                origin.strip() 
                for origin in os.environ["DASHBOARD_CORS_ORIGINS"].split(",")
            ]
            self.security_config.cors_origins = origins
        
        # 允許的IP
        if "DASHBOARD_ALLOWED_IPS" in os.environ:
            ips = [
                ip.strip() 
                for ip in os.environ["DASHBOARD_ALLOWED_IPS"].split(",")
            ]
            self.security_config.allowed_ips = ips
    
    def _validate_config(self) -> None:
        """驗證配置"""
        validation_errors = []
        
        # 驗證告警閾值
        if self.alert_thresholds.email_pending_warning >= self.alert_thresholds.email_pending_critical:
            validation_errors.append("郵件待處理警告閾值應小於嚴重閾值")
        
        if self.alert_thresholds.dramatiq_pending_warning >= self.alert_thresholds.dramatiq_pending_critical:
            validation_errors.append("Dramatiq待處理警告閾值應小於嚴重閾值")
        
        if self.alert_thresholds.cpu_warning >= self.alert_thresholds.cpu_critical:
            validation_errors.append("CPU警告閾值應小於嚴重閾值")
        
        if self.alert_thresholds.memory_warning >= self.alert_thresholds.memory_critical:
            validation_errors.append("記憶體警告閾值應小於嚴重閾值")
        
        if self.alert_thresholds.disk_warning >= self.alert_thresholds.disk_critical:
            validation_errors.append("磁碟警告閾值應小於嚴重閾值")
        
        # 驗證更新間隔
        if self.update_intervals.metrics_collection <= 0:
            validation_errors.append("指標收集間隔必須大於0")
        
        if self.update_intervals.alerts_evaluation <= 0:
            validation_errors.append("告警評估間隔必須大於0")
        
        # 驗證保留策略
        if self.retention_policies.metrics_history <= 0:
            validation_errors.append("指標歷史保留天數必須大於0")
        
        # 驗證WebSocket配置
        if self.websocket_config.max_connections <= 0:
            validation_errors.append("WebSocket最大連接數必須大於0")
        
        # 驗證資料庫配置
        if not self.database_config.db_path:
            validation_errors.append("資料庫路徑不能為空")
        
        if validation_errors:
            error_msg = "配置驗證失敗:\n" + "\n".join(f"- {error}" for error in validation_errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("配置驗證通過")
    
    def save_config(self, config_file: Optional[Path] = None) -> None:
        """保存配置到檔案"""
        target_file = config_file or self.config_file
        target_file.parent.mkdir(parents=True, exist_ok=True)
        
        config_data = {
            "env": self.env,
            "alert_thresholds": self._dataclass_to_dict(self.alert_thresholds),
            "update_intervals": self._dataclass_to_dict(self.update_intervals),
            "retention_policies": self._dataclass_to_dict(self.retention_policies),
            "websocket_config": self._dataclass_to_dict(self.websocket_config),
            "display_config": self._dataclass_to_dict(self.display_config),
            "notification_config": self._notification_config_to_dict(),
            "database_config": self._dataclass_to_dict(self.database_config),
            "security_config": self._dataclass_to_dict(self.security_config),
        }
        
        try:
            with open(target_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {target_file}")
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
            raise
    
    def _dataclass_to_dict(self, obj: Any) -> Dict[str, Any]:
        """將dataclass轉換為字典"""
        if hasattr(obj, '__dataclass_fields__'):
            return {
                field_name: getattr(obj, field_name)
                for field_name in obj.__dataclass_fields__
            }
        return {}
    
    def _notification_config_to_dict(self) -> Dict[str, Any]:
        """將通知配置轉換為字典"""
        config_dict = self._dataclass_to_dict(self.notification_config)
        config_dict["enabled_channels"] = [
            channel.value for channel in self.notification_config.enabled_channels
        ]
        return config_dict
    
    def get_alert_threshold(self, metric_type: str, level: str) -> Optional[Union[int, float]]:
        """獲取告警閾值"""
        threshold_key = f"{metric_type}_{level}"
        return getattr(self.alert_thresholds, threshold_key, None)
    
    def is_development(self) -> bool:
        """是否為開發環境"""
        return self.env == "development"
    
    def is_production(self) -> bool:
        """是否為生產環境"""
        return self.env == "production"
    
    def get_database_url(self) -> str:
        """獲取資料庫URL"""
        db_path = Path(self.database_config.db_path)
        if not db_path.is_absolute():
            db_path = Path.cwd() / db_path
        return f"sqlite:///{db_path}"
    
    def to_dict(self) -> Dict[str, Any]:
        """將配置轉換為字典"""
        return {
            "env": self.env,
            "alert_thresholds": self._dataclass_to_dict(self.alert_thresholds),
            "update_intervals": self._dataclass_to_dict(self.update_intervals),
            "retention_policies": self._dataclass_to_dict(self.retention_policies),
            "websocket_config": self._dataclass_to_dict(self.websocket_config),
            "display_config": self._dataclass_to_dict(self.display_config),
            "notification_config": self._notification_config_to_dict(),
            "database_config": self._dataclass_to_dict(self.database_config),
            "security_config": self._dataclass_to_dict(self.security_config),
        }
    
    # 向後兼容性屬性訪問器
    @property
    def metrics_update_interval(self) -> int:
        """指標更新間隔（向後兼容性）"""
        return self.update_intervals.metrics_collection
    
    @metrics_update_interval.setter
    def metrics_update_interval(self, value: int) -> None:
        """設定指標更新間隔（向後兼容性）"""
        self.update_intervals.metrics_collection = value
    
    @property
    def alerts_check_interval(self) -> int:
        """告警檢查間隔（向後兼容性）"""
        return self.update_intervals.alerts_evaluation
    
    @alerts_check_interval.setter
    def alerts_check_interval(self, value: int) -> None:
        """設定告警檢查間隔（向後兼容性）"""
        self.update_intervals.alerts_evaluation = value
    
    @property
    def trends_update_interval(self) -> int:
        """趨勢更新間隔（向後兼容性）"""
        return self.update_intervals.trends_analysis
    
    @trends_update_interval.setter
    def trends_update_interval(self, value: int) -> None:
        """設定趨勢更新間隔（向後兼容性）"""
        self.update_intervals.trends_analysis = value
    
    @property
    def websocket_heartbeat_interval(self) -> int:
        """WebSocket心跳間隔（向後兼容性）"""
        return self.websocket_config.heartbeat_interval
    
    @websocket_heartbeat_interval.setter
    def websocket_heartbeat_interval(self, value: int) -> None:
        """設定WebSocket心跳間隔（向後兼容性）"""
        self.websocket_config.heartbeat_interval = value
    
    @property
    def metrics_retention_days(self) -> int:
        """指標保留天數（向後兼容性）"""
        return self.retention_policies.metrics_history
    
    @metrics_retention_days.setter
    def metrics_retention_days(self, value: int) -> None:
        """設定指標保留天數（向後兼容性）"""
        self.retention_policies.metrics_history = value
    
    @property
    def alerts_retention_days(self) -> int:
        """告警保留天數（向後兼容性）"""
        return self.retention_policies.alerts_history
    
    @alerts_retention_days.setter
    def alerts_retention_days(self, value: int) -> None:
        """設定告警保留天數（向後兼容性）"""
        self.retention_policies.alerts_history = value
    
    @property
    def max_websocket_connections(self) -> int:
        """最大WebSocket連接數（向後兼容性）"""
        return self.websocket_config.max_connections
    
    @max_websocket_connections.setter
    def max_websocket_connections(self, value: int) -> None:
        """設定最大WebSocket連接數（向後兼容性）"""
        self.websocket_config.max_connections = value


# 全域配置實例
_dashboard_config: Optional[DashboardConfig] = None


def get_dashboard_config() -> DashboardConfig:
    """獲取全域儀表板配置實例"""
    global _dashboard_config
    
    if _dashboard_config is None:
        env = os.getenv("DASHBOARD_ENV", "development")
        _dashboard_config = DashboardConfig(env=env)
    
    return _dashboard_config


def init_dashboard_config(env: str = "development", config_file: Optional[Path] = None) -> DashboardConfig:
    """初始化儀表板配置"""
    global _dashboard_config
    _dashboard_config = DashboardConfig(env=env, config_file=config_file)
    return _dashboard_config


def reload_dashboard_config() -> DashboardConfig:
    """重新載入儀表板配置"""
    global _dashboard_config
    if _dashboard_config:
        env = _dashboard_config.env
        config_file = _dashboard_config.config_file
        _dashboard_config = DashboardConfig(env=env, config_file=config_file)
    else:
        _dashboard_config = DashboardConfig()
    
    return _dashboard_config